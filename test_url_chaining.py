#!/usr/bin/env python3
"""
Test script to validate URL node chaining and loop detection functionality.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models import (
    NodeData, NodePosition, ChatbotNodeCreate, RuleBasedChatbotFlow, ChatbotEdgeCreate
)
from app.services.loop_detection_service import LoopDetectionService

def test_url_node_chaining_types():
    """Test that URL nodes are included in chainable node types"""
    print("🧪 Testing URL node chaining types...")
    
    # Test chaining logic constants
    chainable_types = ["sendMessage", "question", "buttons", "list", "url"]
    
    assert "url" in chainable_types, "URL should be in chainable node types"
    print("✅ URL node is included in chainable types")

def test_url_node_loop_detection_types():
    """Test that URL nodes are properly categorized in loop detection"""
    print("🧪 Testing URL node loop detection types...")
    
    loop_service = LoopDetectionService()
    
    # URL nodes should be in user_input_node_types (break infinite loops)
    assert "url" in loop_service.user_input_node_types, "URL should be in user_input_node_types"
    
    # URL nodes should NOT be in auto_advance_node_types
    assert "url" not in loop_service.auto_advance_node_types, "URL should not be in auto_advance_node_types"
    
    print("✅ URL node is properly categorized in loop detection")
    print(f"   User input types: {loop_service.user_input_node_types}")
    print(f"   Auto advance types: {loop_service.auto_advance_node_types}")

def test_url_node_safe_loop():
    """Test that loops containing URL nodes are considered safe"""
    print("🧪 Testing URL node safe loop detection...")
    
    loop_service = LoopDetectionService()
    
    # Create a cycle with URL node: question -> url -> question
    cycle_nodes = ["question_node", "url_node", "question_node"]
    node_type_map = {
        "question_node": "question",
        "url_node": "url"
    }
    
    # This should be considered a safe loop (not infinite)
    is_infinite = loop_service._is_infinite_loop(cycle_nodes[:-1], node_type_map)  # Remove duplicate end node
    
    assert not is_infinite, "Loop with URL node should not be considered infinite"
    print("✅ Loop containing URL node is correctly identified as safe")

def test_url_node_chaining_flow():
    """Test creating a flow with URL node chaining"""
    print("🧪 Testing URL node chaining flow creation...")
    
    # Create flow with chaining: sendMessage -> URL -> question
    flow = RuleBasedChatbotFlow(
        nodes=[
            ChatbotNodeCreate(
                id="send_node",
                name="send_message",
                type="sendMessage",
                position=NodePosition(x=100, y=100),
                isFirstNode=True,
                data=NodeData(
                    text="Welcome! Here's some information."
                ),
                variableMapping=[]
            ),
            ChatbotNodeCreate(
                id="url_node",
                name="url_node",
                type="url",
                position=NodePosition(x=200, y=100),
                isFirstNode=False,
                data=NodeData(
                    header={
                        "format": "text",
                        "text": "Visit our website",
                        "mediaFile": None
                    },
                    body="Click the button below to visit our website",
                    footer="Thank you for your interest",
                    buttonText="Visit Website",
                    ctaURL="https://example.com"
                ),
                variableMapping=[]
            ),
            ChatbotNodeCreate(
                id="question_node",
                name="question_node",
                type="question",
                position=NodePosition(x=300, y=100),
                isFirstNode=False,
                data=NodeData(
                    text="What's your name?"
                ),
                variableMapping=[]
            )
        ],
        edges=[
            ChatbotEdgeCreate(
                id="edge1",
                source="send_message",
                target="url_node",
                sourceHandle=None,
                targetHandle=None
            ),
            ChatbotEdgeCreate(
                id="edge2",
                source="url_node",
                target="question_node",
                sourceHandle=None,
                targetHandle=None
            )
        ]
    )
    
    # Verify flow structure
    assert len(flow.nodes) == 3
    assert len(flow.edges) == 2
    assert flow.nodes[1].type == "url"
    assert flow.edges[0].target == "url_node"
    assert flow.edges[1].source == "url_node"
    
    print("✅ URL node chaining flow created successfully")
    print(f"   Flow: {flow.edges[0].source} → {flow.edges[0].target} → {flow.edges[1].target}")

def test_url_node_terminal_flow():
    """Test URL node as terminal node"""
    print("🧪 Testing URL node as terminal node...")
    
    # Create flow where URL node is terminal (no outgoing edges)
    flow = RuleBasedChatbotFlow(
        nodes=[
            ChatbotNodeCreate(
                id="question_node",
                name="question_node",
                type="question",
                position=NodePosition(x=100, y=100),
                isFirstNode=True,
                data=NodeData(
                    text="Would you like to visit our website?",
                    options=[
                        {"id": "yes", "text": "Yes"},
                        {"id": "no", "text": "No"}
                    ]
                ),
                variableMapping=[]
            ),
            ChatbotNodeCreate(
                id="url_terminal",
                name="url_terminal",
                type="url",
                position=NodePosition(x=200, y=100),
                isFirstNode=False,
                data=NodeData(
                    header={
                        "format": "text",
                        "text": "Visit our website",
                        "mediaFile": None
                    },
                    body="Click the button below to visit our website. This will end our conversation.",
                    footer="Thank you for your interest",
                    buttonText="Visit Website",
                    ctaURL="https://example.com"
                ),
                variableMapping=[]
            )
        ],
        edges=[
            ChatbotEdgeCreate(
                id="edge1",
                source="question_node",
                target="url_terminal",
                sourceHandle="yes",
                targetHandle=None
            )
        ]
    )
    
    # Verify terminal URL node structure
    assert len(flow.nodes) == 2
    assert len(flow.edges) == 1
    assert flow.nodes[1].type == "url"
    assert flow.edges[0].target == "url_terminal"
    
    # Check that URL node has no outgoing edges (terminal)
    url_outgoing_edges = [edge for edge in flow.edges if edge.source == "url_terminal"]
    assert len(url_outgoing_edges) == 0, "Terminal URL node should have no outgoing edges"
    
    print("✅ URL node terminal behavior validated")

def main():
    """Run all URL node chaining and loop detection tests"""
    print("🚀 Starting URL node chaining and loop detection tests...\n")
    
    try:
        test_url_node_chaining_types()
        test_url_node_loop_detection_types()
        test_url_node_safe_loop()
        test_url_node_chaining_flow()
        test_url_node_terminal_flow()
        
        print("\n🎉 All URL node chaining and loop detection tests passed!")
        print("✅ URL node chaining logic is working correctly")
        print("✅ URL node loop detection is working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
