from sqlalchemy import Column, <PERSON>, Integer, BigInteger, DateTime, ForeignKey, Boolean, Text, JSON, UniqueConstraint, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator, model_validator
from app.exceptions import QuestionLengthValidationError

Base = declarative_base()


def to_camel_case(snake_str: str) -> str:
    """Convert snake_case to camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


class CamelCaseModel(BaseModel):
    """Base model that converts field names to camelCase in responses"""

    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    email = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Document(Base):
    __tablename__ = "documents"

    id = Column(String, primary_key=True)
    tenant_id = Column(BigInteger, nullable=False)
    document_name = Column(String, nullable=False)
    document_type = Column(String, nullable=False)
    es_index = Column(String)
    es_document_id = Column(String)
    s3_key = Column(String)  # Add S3 key field
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String)

class Chatbot(Base):
    __tablename__ = "chatbots"

    id = Column(String, primary_key=True, index=True)
    tenant_id = Column(BigInteger, index=True)
    name = Column(String)
    type = Column(String, default="AI")  # 'AI' or 'RULE'
    description = Column(String, nullable=True)
    status = Column(String, default="DRAFT")  # 'DRAFT', 'ACTIVE', 'INACTIVE'
    welcome_message = Column(String, nullable=True)
    thank_you_message = Column(String, nullable=True)
    # Connected account fields
    connected_account_display_name = Column(String, nullable=True)
    connected_account_id = Column(Integer, nullable=True)
    # Trigger field for entity creation flow
    trigger = Column(String, nullable=True)  # 'NEW_ENTITY' or 'EXISTING_ENTITY'
    # Entities configuration - stores which entities this chatbot applies to
    entities = Column(JSON, nullable=True)  # List of entities: [{"id": "LEAD", "name": "lead"}, ...]
    # User tracking fields
    created_by = Column(String, nullable=True)  # User ID who created the chatbot
    updated_by = Column(String, nullable=True)  # User ID who last updated the chatbot
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    questions = relationship("ChatbotQuestion", back_populates="chatbot", cascade="all, delete-orphan")
    knowledgebases = relationship("ChatbotKnowledgebase", back_populates="chatbot", cascade="all, delete-orphan")
    nodes = relationship("ChatbotNode", back_populates="chatbot", cascade="all, delete-orphan")
    edges = relationship("ChatbotEdge", back_populates="chatbot", cascade="all, delete-orphan")
    credit_usage = relationship("ChatbotCreditUsage", back_populates="chatbot", cascade="all, delete-orphan")

class ChatbotQuestion(Base):
    __tablename__ = "chatbot_questions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), nullable=False, index=True)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    question = Column(String(255), nullable=False)
    position = Column(Integer, nullable=False, default=1)  # Position/order of the question (1-6)
    user_questions = Column(Text, nullable=True)  # JSON array of user questions for position 6
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    chatbot = relationship("Chatbot", back_populates="questions")
    entity_fields = relationship("ChatbotQuestionEntityField", back_populates="question", cascade="all, delete-orphan")

class ChatbotQuestionEntityField(Base):
    __tablename__ = "chatbot_question_entity_fields"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    question_id = Column(String, ForeignKey("chatbot_questions.id"), nullable=False, index=True)
    entity_type = Column(String, nullable=False)  # Entity type for the field mapping
    field_id = Column(Integer, nullable=False)  # Field ID for this entity type
    name = Column(String, nullable=False)  # Field name for this entity type
    display_name = Column(String, nullable=False)  # Display name for this entity field
    standard = Column(Boolean, nullable=False, default=False)  # Whether this is a standard field
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship back to question
    question = relationship("ChatbotQuestion", back_populates="entity_fields")

# New models for rule-based chatbots
class ChatbotNode(Base):
    __tablename__ = "chatbot_nodes"
    __table_args__ = (
        # Composite unique constraint: node_id should be unique per chatbot
        UniqueConstraint('chatbot_id', 'node_id', name='uq_chatbot_node_id'),
    )

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), nullable=False, index=True)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    node_id = Column(String(255), nullable=False, index=True)  # Frontend node ID (e.g., "123333-1") - unique per chatbot, max 255 chars
    name = Column(String, nullable=False)  # Node name (e.g., "n1")
    type = Column(String, nullable=False)  # Node type: question, buttons, list, sendMessage, condition
    position_x = Column(Float, nullable=False)  # X coordinate
    position_y = Column(Float, nullable=False)  # Y coordinate
    data = Column(JSON, nullable=True)  # Node-specific data (text, options, buttons, etc.)
    variable_mapping = Column(JSON, nullable=True)  # Variable mapping configuration
    is_first_node = Column(Boolean, nullable=False, default=False, index=True)  # NEW: Store if this is a first node
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    chatbot = relationship("Chatbot", back_populates="nodes")
    entity_fields = relationship("ChatbotNodeEntityField", back_populates="node", cascade="all, delete-orphan", 
                                primaryjoin="ChatbotNode.node_id == foreign(ChatbotNodeEntityField.node_id)")
    outgoing_edges = relationship("ChatbotEdge", back_populates="source_node_rel",
                                 primaryjoin="ChatbotNode.node_id == foreign(ChatbotEdge.source_node)")
    incoming_edges = relationship("ChatbotEdge", back_populates="target_node_rel",
                                 primaryjoin="ChatbotNode.node_id == foreign(ChatbotEdge.target_node)")

class ChatbotNodeEntityField(Base):
    __tablename__ = "chatbot_node_entity_fields"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    node_id = Column(String(255), nullable=False, index=True)  # References node_id for logical relationship (no FK constraint)
    entity_type = Column(String, nullable=False)  # Entity type for the field mapping
    field_id = Column(Integer, nullable=False)  # Field ID for this entity type
    name = Column(String, nullable=False)  # Field name for this entity type
    display_name = Column(String, nullable=False)  # Display name for this entity field
    standard = Column(Boolean, nullable=False, default=False)  # Whether this is a standard field
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship back to node (maintained at application level)
    node = relationship("ChatbotNode", back_populates="entity_fields", 
                       primaryjoin="foreign(ChatbotNodeEntityField.node_id) == ChatbotNode.node_id")

class ChatbotEdge(Base):
    __tablename__ = "chatbot_edges"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), nullable=False, index=True)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    edge_id = Column(String, nullable=False)  # Frontend edge ID (e.g., "e1")
    source_node = Column(String(255), nullable=False, index=True)  # References node_id for logical relationship (no FK constraint)
    source_handle = Column(String, nullable=True)  # Source handle (e.g., "h1")
    target_node = Column(String(255), nullable=False, index=True)  # References node_id for logical relationship (no FK constraint)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    chatbot = relationship("Chatbot", back_populates="edges")
    source_node_rel = relationship("ChatbotNode", back_populates="outgoing_edges", 
                                  primaryjoin="foreign(ChatbotEdge.source_node) == ChatbotNode.node_id")
    target_node_rel = relationship("ChatbotNode", back_populates="incoming_edges", 
                                  primaryjoin="foreign(ChatbotEdge.target_node) == ChatbotNode.node_id")

class ChatbotKnowledgebase(Base):
    __tablename__ = "chatbot_knowledgebases"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), nullable=False, index=True)
    document_id = Column(String, nullable=False, index=True)
    document_name = Column(String, nullable=True)  # New field for document name
    document_size = Column(BigInteger, nullable=True)  # New field for document size in bytes
    document_type = Column(String, nullable=True)  # New field for document type (pdf, docx, etc.)
    tenant_id = Column(BigInteger, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    chatbot = relationship("Chatbot", back_populates="knowledgebases")

class TenantIndexMapping(Base):
    """
    Tracks which master Elasticsearch index each tenant is assigned to.
    This enables the master index + alias strategy for multi-tenant data organization.
    """
    __tablename__ = "tenant_index_mappings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    tenant_id = Column(BigInteger, nullable=False, unique=True, index=True)
    master_index_number = Column(Integer, nullable=False, index=True)
    alias_name = Column(String, nullable=False, unique=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class ChatbotConversation(Base):
    __tablename__ = "chatbot_conversations"

    id = Column(String, primary_key=True, index=True)
    chatbot_id = Column(String, ForeignKey("chatbots.id"))
    tenant_id = Column(BigInteger, index=True)
    user_id = Column(String, index=True)  # Add user_id field
    conversation_data = Column(Text)
    completed = Column(Boolean, default=False)
    # Entity details for this conversation
    entity_details = Column(JSON, nullable=True)  # Store entity IDs and types
    connected_account_id = Column(Integer, nullable=True, index=True)  # Connected account ID
    connected_account_name = Column(String, nullable=True)  # Connected account name
    entity_update_status = Column(JSON, nullable=True)  # Track entity update results
    message_conversation_id = Column(Integer, nullable=True, index=True)  # Message conversation ID for workflow integration
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    token_usage = relationship(
        "ConversationTokenUsage", 
        back_populates="conversation",
        cascade="all, delete-orphan"
    )
    credit_usage = relationship("ChatbotCreditUsage", back_populates="conversation", cascade="all, delete-orphan")

class ConversationTokenUsage(Base):
    __tablename__ = "conversation_token_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String, ForeignKey("chatbot_conversations.id"), index=True)
    tenant_id = Column(BigInteger, index=True)
    input = Column(JSON)  # JSONB column containing user message and system prompt
    output = Column(JSON)  # JSONB column containing LLM-generated response
    input_tokens = Column(Integer, default=0)  # Tokens in input prompt sent to LLM
    output_tokens = Column(Integer, default=0)  # Tokens in response generated by LLM
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationship with conversation
    conversation = relationship("ChatbotConversation", back_populates="token_usage")

class ChatbotCreditUsage(Base):
    __tablename__ = "chatbot_credit_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    chatbot_id = Column(String, ForeignKey("chatbots.id"), index=True)
    conversation_id = Column(String, ForeignKey("chatbot_conversations.id"), index=True)
    tenant_id = Column(BigInteger, index=True)
    question = Column(String, nullable=False)  # The question that was asked
    answer = Column(String, nullable=False)    # The answer that was provided
    credits_used = Column(Integer, default=0)  # Credits consumed (1 or 2)
    has_knowledgebase = Column(Boolean, default=False)  # Whether knowledgebase was available
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    chatbot = relationship("Chatbot", back_populates="credit_usage")
    conversation = relationship("ChatbotConversation", back_populates="credit_usage")

# JWT Token models
class TokenAction(BaseModel):
    model_config = ConfigDict(extra='ignore')
    
    read: bool = False
    write: bool = False
    update: bool = False
    delete: bool = False
    email: bool = False
    call: bool = False
    sms: bool = False
    task: bool = False
    note: bool = False
    meeting: bool = False
    document: bool = False
    readAll: bool = False
    updateAll: bool = False
    deleteAll: bool = False
    quotation: bool = False
    reshare: bool = False
    reassign: bool = False

class TokenPermission(BaseModel):
    model_config = ConfigDict(extra='ignore')

    id: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
    limits: Optional[int] = None
    units: Optional[str] = None
    action: Optional[TokenAction] = None

class TokenSource(BaseModel):
    model_config = ConfigDict(extra='ignore')

    type: Optional[str] = None
    id: Optional[str] = None
    name: Optional[str] = None

class TokenMeta(BaseModel):
    model_config = ConfigDict(extra='ignore')

    rate_limit: Optional[int] = Field(default=None, alias="rate-limit")
    pid: Optional[int] = None

class TokenData(BaseModel):
    model_config = ConfigDict(extra='ignore')

    accessToken: str
    expiresIn: Optional[int] = None
    expiry: Optional[Union[int, str]] = None  # Allow both int and string timestamps
    tokenType: Optional[str] = "Bearer"
    refreshToken: Optional[str] = None
    permissions: Optional[List[TokenPermission]] = []
    userId: Union[str, int]  # Allow both string and int
    username: Optional[str] = None
    tenantId: Union[str, int]  # Allow both string and int
    source: Optional[TokenSource] = None
    meta: Optional[TokenMeta] = None

    @field_validator('expiry', mode='before')
    @classmethod
    def validate_expiry(cls, v):
        """Convert string timestamps to integers or keep as is"""
        if v is None:
            return v
        if isinstance(v, str):
            # If it's a timestamp string, try to parse it
            try:
                # Parse ISO format timestamp and convert to Unix timestamp
                dt = datetime.fromisoformat(v.replace('Z', '+00:00'))
                return int(dt.timestamp())
            except (ValueError, AttributeError):
                # If parsing fails, return the string as is
                return v
        return v

class JWTPayload(BaseModel):
    model_config = ConfigDict(extra='ignore')

    iss: Optional[str] = None
    data: Optional[TokenData] = None

    # Alternative flat structure support
    accessToken: Optional[str] = None
    userId: Optional[Union[str, int]] = None
    tenantId: Optional[Union[str, int]] = None
    permissions: Optional[List[TokenPermission]] = []
    username: Optional[str] = None
    refreshToken: Optional[str] = None
    source: Optional[TokenSource] = None
    meta: Optional[TokenMeta] = None

# Pydantic models for API requests/responses
class EntityFieldMapping(BaseModel):
    entityType: Optional[str] = None
    fieldId: Optional[int] = None
    name: Optional[str] = None
    displayName: Optional[str] = None
    standard: bool = False  # Keep as non-optional with default False

class QuestionCreate(BaseModel):
    question: str = Field(..., description="Question text (max 255 characters)")
    position: int = 1  # Position 1-5: predefined questions, Position 6: field config for user questions
    entityFields: Optional[List[EntityFieldMapping]] = None  # Allow null for entity_fields

    @field_validator('question')
    @classmethod
    def validate_question_length(cls, v):
        if v is None:
            return v
        if len(v) > 255:
            raise QuestionLengthValidationError(
                question=v,
                max_length=255
            )
        return v

class QuestionUpdate(BaseModel):
    question: Optional[str] = Field(None, description="Question text (max 255 characters)")
    position: Optional[int] = None  # Position 1-5: predefined questions, Position 6: field config for user questions
    entityFields: Optional[List[EntityFieldMapping]] = None  # Allow null for entity_fields

    @field_validator('question')
    @classmethod
    def validate_question_length(cls, v):
        if v is None:
            return v
        if len(v) > 255:
            raise QuestionLengthValidationError(
                question=v,
                max_length=255
            )
        return v

# Legacy models for backward compatibility (deprecated)
class QuestionCreateLegacy(BaseModel):
    question: str = Field(..., description="Question text (max 255 characters)")
    fieldId: int
    displayName: str
    entityType: str
    name: str
    standard: bool = False

    @field_validator('question')
    @classmethod
    def validate_question_length(cls, v):
        if v is None:
            return v
        if len(v) > 255:
            raise QuestionLengthValidationError(
                question=v,
                max_length=255
            )
        return v

class QuestionUpdateLegacy(BaseModel):
    question: str = Field(None, description="Question text (max 255 characters)")
    fieldId: int = None
    displayName: str = None
    entityType: str = None
    name: str = None
    standard: bool = None

    @field_validator('question')
    @classmethod
    def validate_question_length(cls, v):
        if v is None:
            return v
        if len(v) > 255:
            raise QuestionLengthValidationError(
                question=v,
                max_length=255
            )
        return v

class ConnectedAccount(BaseModel):
    displayName: str
    accountId: int  # Removed entityType - not needed for account-based chatbots

class EntityConfig(BaseModel):
    """Entity configuration for chatbot"""
    entity: str  # Entity type like "LEAD", "CONTACT", "DEAL"

class ChatbotCreate(BaseModel):
    name: str
    type: str  # 'AI' or 'RULE'
    description: Optional[str] = None
    welcomeMessage: Optional[str] = None
    thankYouMessage: Optional[str] = None
    connectedAccount: Optional[ConnectedAccount] = None
    trigger: Optional[str] = None  # Keep trigger field - 'NEW_ENTITY' or 'EXISTING_ENTITY'
    entities: Optional[List[EntityConfig]] = None  # List of entities this chatbot applies to
    
    @field_validator('welcomeMessage', 'thankYouMessage')
    @classmethod
    def validate_messages_for_ai_chatbots(cls, v, info):
        """Validate that AI chatbots have welcome and thank you messages"""
        if info.data.get('type', '').upper() == 'AI':
            if v is None or (isinstance(v, str) and not v.strip()):
                field_name = info.field_name
                raise ValueError(f"{field_name} is required for AI type chatbots")
        return v

class ChatbotUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    welcomeMessage: Optional[str] = None
    thankYouMessage: Optional[str] = None
    connectedAccount: Optional[ConnectedAccount] = None
    trigger: Optional[str] = None  # Keep trigger field - 'NEW_ENTITY' or 'EXISTING_ENTITY'
    entities: Optional[List[EntityConfig]] = None  # List of entities this chatbot applies to
    questions: Optional[List[QuestionCreate]] = None
    knowledgebase_ids: Optional[List[str]] = None
    nodes: Optional[List[Dict[str, Any]]] = None  # For rule-based chatbots
    edges: Optional[List[Dict[str, Any]]] = None  # For rule-based chatbots
    
    @field_validator('welcomeMessage', 'thankYouMessage')
    @classmethod
    def validate_messages_for_ai_chatbots(cls, v, info):
        """Validate that AI chatbots have welcome and thank you messages"""
        if info.data.get('type', '').upper() == 'AI':
            if v is None or (isinstance(v, str) and not v.strip()):
                field_name = info.field_name
                raise ValueError(f"{field_name} is required for AI type chatbots")
        return v

class ConversationMessage(BaseModel):
    message: str

class EntityDetail(BaseModel):
    id: int
    entityType: str

class ConnectedAccountDetail(BaseModel):
    id: int
    name: str

class ConversationRequest(BaseModel):
    message: str
    entityDetails: List[EntityDetail]
    connectedAccount: ConnectedAccountDetail
    messageConversationId: Optional[Union[str, int]] = None

class ConversationResponse(BaseModel):
    chatbotConversationId: str
    message: str
    firstQuestion: Optional[str] = None  # Include first question in response

class CreditUsageResponse(BaseModel):
    id: str
    chatbot_id: str
    conversation_id: str
    question: str
    answer: str
    credits_used: int
    has_knowledgebase: bool
    timestamp: datetime
    message: str = None
    completed: bool = False
    verification_pending: bool = False
    answers: List[dict] = None
    is_knowledge_response: bool = False
    is_off_topic: bool = False
    ended: bool = False

# Knowledgebase-related Pydantic models
class KnowledgebaseDocument(BaseModel):
    documentId: Optional[str] = None  # None for new documents
    documentName: str
    documentSize: int  # Size in bytes
    documentType: str  # File extension or MIME type

class KnowledgebaseUpdateRequest(BaseModel):
    documents: List[KnowledgebaseDocument]

# New Pydantic models for rule-based chatbots
class NodePosition(BaseModel):
    x: float
    y: float

class MediaFile(BaseModel):
    fileId: int
    fileName: str
    fileSize: int
    fileType: str
    fileCaption: Optional[str] = None

class NodeOption(BaseModel):
    type: Optional[str] = None  # "text" or "media"
    text: Optional[str] = None
    name: Optional[str] = None  # Optional for sendMessage nodes
    position: Optional[int] = None
    mediaFile: Optional[MediaFile] = None  # strongly typed media metadata

    @model_validator(mode="after")
    def validate_option(self):
        option_type = (self.type or "text").lower()
        if option_type == "text":
            if not self.text:
                raise ValueError("text is required when type is 'text'")
        elif option_type == "media":
            if self.mediaFile is None:
                raise ValueError("mediaFile is required when type is 'media'")
        return self

class MediaFile(BaseModel):
    fileId: int
    fileSize: int
    fileType: str
    fileName: str

class NodeHeader(BaseModel):
    format: str  # text/image/video/document
    text: Optional[str] = None
    mediaFile: Optional[MediaFile] = None

class NodeButton(BaseModel):
    name: str
    text: str
    position: int

class NodeSection(BaseModel):
    id: Optional[str] = None
    title: str
    position: Optional[int] = None
    rows: List[Dict[str, Any]]

class NodeData(BaseModel):
    text: Optional[str] = None
    options: Optional[List[NodeOption]] = None
    isMedia: Optional[bool] = False
    dataVariable: Optional[str] = None
    entityFields: Optional[List[Dict[str, Any]]] = None
    header: Optional[NodeHeader] = None
    footer: Optional[str] = None
    buttons: Optional[List[NodeButton]] = None
    body: Optional[str] = None
    menuButton: Optional[str] = None
    sections: Optional[List[NodeSection]] = None
    conditions: Optional[List[Dict[str, str]]] = None
    operator: Optional[str] = None
    # URL node specific fields
    buttonText: Optional[str] = None  # Display text for the CTA button
    ctaURL: Optional[str] = None      # The URL to redirect to
    
    @field_validator('header', mode='before')
    @classmethod
    def validate_header(cls, v):
        """Convert string header to NodeHeader object and handle mediaFile structure"""
        if v is None:
            return None
        if isinstance(v, str):
            # Convert string to NodeHeader object
            return {"format": "text", "text": v, "mediaFile": None}
        if isinstance(v, dict):
            # Ensure all required fields are present
            if "format" not in v:
                v["format"] = "text"
            
            # Handle text vs mediaFile based on format
            if v["format"] == "text":
                if "text" not in v:
                    v["text"] = ""
                v["mediaFile"] = None
            else:
                # For non-text formats, text is optional and mediaFile is required
                if "text" not in v:
                    v["text"] = None
                if "mediaFile" not in v:
                    v["mediaFile"] = None
        return v
    
    @field_validator('buttons', mode='before')
    @classmethod
    def validate_buttons(cls, v):
        """Convert button objects to ensure name field exists"""
        if v is None:
            return None
        if isinstance(v, list):
            validated_buttons = []
            for btn in v:
                if isinstance(btn, dict):
                    # Ensure name field exists (use id, name, or text as fallback)
                    if "name" not in btn:
                        btn["name"] = btn.get("id") or btn.get("text", "")
                validated_buttons.append(btn)
            return validated_buttons
        return v
    


class ChatbotNodeCreate(BaseModel):
    id: str = Field(..., max_length=255, description="Frontend node ID (max 255 characters)")  # Frontend node ID
    name: str  # Node name
    type: str  # question, button, list, sendMessage, condition
    position: NodePosition
    data: NodeData
    variableMapping: Optional[List[Dict[str, Any]]] = []
    isFirstNode: Optional[bool] = False  # New field to identify first nodes
    
    @field_validator('id')
    @classmethod
    def validate_node_id(cls, v):
        if not v or not v.strip():
            raise ValueError('Node ID cannot be empty')
        if len(v) > 255:
            raise ValueError(f'Node ID exceeds maximum length of 255 characters. Current length: {len(v)} characters')
        return v

class ChatbotEdgeCreate(BaseModel):
    id: str  # Frontend edge ID (mapped to edge_id column)
    source: str  # Source node name
    sourceHandle: Optional[str] = None  # Source handle (can be null)
    target: str  # Target node name
    targetHandle: Optional[str] = None  # Target handle (can be null)

class RuleBasedChatbotFlow(BaseModel):
    nodes: List[ChatbotNodeCreate]
    edges: List[ChatbotEdgeCreate]

class ChatbotNodeResponse(BaseModel):
    id: str
    node_id: str
    name: str
    type: str
    position_x: float
    position_y: float
    data: Dict[str, Any]
    variable_mapping: Optional[List[Dict[str, Any]]] = []
    entity_fields: List[Dict[str, Any]] = []
    created_at: datetime
    updated_at: datetime

class ChatbotEdgeResponse(BaseModel):
    id: str
    edge_id: str
    source_node: str
    source_handle: str
    target_node: str
    created_at: datetime
    updated_at: datetime

class RuleBasedChatbotFlowResponse(BaseModel):
    nodes: List[ChatbotNodeResponse]
    edges: List[ChatbotEdgeResponse]

# User-related Pydantic models
class UserResponse(BaseModel):
    id: str
    name: str

class ChatbotListResponse(BaseModel):
    id: str
    name: str
    type: str
    description: str = None
    status: str
    welcomeMessage: str = None
    thankYouMessage: str = None
    connectedAccountDisplayName: str = None
    connectedAccountId: int = None
    trigger: str = None  # Keep trigger field
    # Removed entityType - not needed for account-based chatbots
    createdBy: UserResponse = None
    updatedBy: UserResponse = None
    createdAt: datetime
    updatedAt: datetime


# Response models with camelCase serialization
class EntityFieldMappingResponse(CamelCaseModel):
    entity_type: Optional[str] = None
    field_id: Optional[int] = None
    name: Optional[str] = None
    display_name: Optional[str] = None
    standard: Optional[bool] = False


class QuestionResponse(CamelCaseModel):
    id: str
    question: str
    position: int  # Always include position in responses
    entity_fields: Optional[List[EntityFieldMappingResponse]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ChatbotResponseCamelCase(CamelCaseModel):
    id: str
    name: str
    tenant_id: int
    status: str
    trigger: Optional[str] = None
    questions: Optional[List[QuestionResponse]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


# Enhanced knowledgebase file upload models
class KnowledgebaseFileMetadata(BaseModel):
    documentName: str
    documentSize: int
    documentType: str

# Background task response model
class BackgroundTaskResponse(BaseModel):
    status: str
    message: str
    task_id: str
    chatbot_id: str
    files_count: int
    processing_type: str
