"""
Test cases for rule-based chatbot rules endpoint and dependent methods.

This module tests:
1. POST /{chatbot_id}/rules endpoint
2. RuleBasedChatbotService.create_flow() method
3. RuleBasedChatbotService.validate_flow() method
4. RuleBasedChatbotService._create_nodes() method
5. RuleBasedChatbotService._create_edges() method
6. RuleBasedChatbotService._delete_existing_flow() method
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from fastapi import HTTPException
from pydantic import ValidationError

from app.main import app
from app.models import (
    Chatbot, ChatbotNode, ChatbotNodeEntityField, ChatbotEdge,
    RuleBasedChatbotFlow, ChatbotNodeCreate, ChatbotEdgeCreate,
    NodePosition, NodeData, NodeOption, NodeButton, NodeSection, NodeHeader,
    ChatbotNodeResponse
)
from app.services.rule_based_chatbot_service import RuleBasedChatbotService
from app.dependencies import Auth<PERSON>ontext
from app.exceptions import RuleBasedFlowValidationError, RuleBasedFlowCreationError, RuleBasedEdgeValidationError, RuleBasedFlowLoopDetectionError


class TestRuleBasedChatbotService:
    """Test cases for RuleBasedChatbotService"""
    
    def test_validate_flow_success(self):
        """Test successful flow validation"""
        print("🧪 Testing validate_flow with valid data...")
        
        service = RuleBasedChatbotService()
        
        # Create valid flow data
        valid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="What is your name?",
                        options=[NodeOption(text="Continue", name="continue", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="end",
                    type="sendMessage",
                    position=NodePosition(x=300, y=100),
                    data=NodeData(
                        text="Thank you for your name!"
                    ),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="start",
                    target="end",
                    sourceHandle="continue",
                    targetHandle="start"
                )
            ]
        )
        
        try:
            service.validate_flow(valid_flow)
            print("✅ validate_flow passed with valid data")
            assert True
        except Exception as e:
            print(f"❌ validate_flow failed: {e}")
            assert False, f"Flow validation should pass: {e}"

    def test_validate_flow_missing_start_node(self):
        """Test flow validation with empty flow"""
        print("🧪 Testing validate_flow with empty flow...")
        
        service = RuleBasedChatbotService()
        
        # Create empty flow
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[],
            edges=[]
        )
        
        try:
            service.validate_flow(invalid_flow)
            print("❌ validate_flow should have failed")
            assert False, "Should have failed validation due to empty flow"
        except RuleBasedFlowValidationError as e:
            print(f"✅ validate_flow correctly failed: {e}")
            # Verify the exception has the proper format
            assert e.error_code == "041120"
            assert "validation failed" in e.message.lower()
            assert "validationErrors" in e.details
            assert len(e.details["validationErrors"]) > 0
            
            # Verify enhanced error structure
            assert "nodeErrors" in e.details
            assert "edgeErrors" in e.details
            assert "summary" in e.details
            assert e.details["summary"]["totalErrors"] > 0
            
            # Test the error resource conversion
            error_resource = e.to_error_resource()
            assert error_resource.errorCode == "041120"
            assert error_resource.message == e.message
            assert len(error_resource.fieldErrors) > 0
            
            # For empty flow, we expect general flow errors, not node-specific errors
            field_error_messages = [fe.message for fe in error_resource.fieldErrors]
            has_general_error = any("Flow must contain at least one node" in msg for msg in field_error_messages)
            assert has_general_error, "Field errors should contain general flow validation error"

    @patch('app.services.rule_based_chatbot_service.Session')
    def test_create_flow_validation_error(self, mock_session):
        """Test create_flow method with validation errors"""
        print("🧪 Testing create_flow with validation errors...")
        
        service = RuleBasedChatbotService()
        mock_db = Mock()
        
        # Create invalid flow data (missing start node)
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-2",
                    name="end",
                    type="message",
                    position=NodePosition(x=300, y=100),
                    data=NodeData(
                        text="This should fail",
                        options=[]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        try:
            service.create_flow(mock_db, "chatbot-123", 12345, invalid_flow)
            print("❌ create_flow should have failed")
            assert False, "Should have failed validation"
        except RuleBasedFlowValidationError as e:
            print(f"✅ create_flow correctly failed: {e}")
            # Verify rollback was called
            mock_db.rollback.assert_called_once()
            # Verify the exception has the proper format
            assert e.error_code == "041120"
            assert "validation failed" in e.message.lower()

    def test_validate_flow_with_node_specific_errors(self):
        """Test flow validation with specific node validation errors"""
        print("🧪 Testing validate_flow with node-specific errors...")
        
        service = RuleBasedChatbotService()
        
        # Create flow with multiple node errors
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="invalid_question",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="",  # Missing text - should cause error
                        options=[]  # Missing options - should cause error
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="invalid_button",
                    type="button",
                    position=NodePosition(x=200, y=100),
                    data=NodeData(
                        text="Button text",
                        buttons=[]  # Missing buttons - should cause error
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-3",
                    name="invalid_type",
                    type="invalid_type",  # Invalid node type
                    position=NodePosition(x=300, y=100),
                    data=NodeData(text="Some text"),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        try:
            service.validate_flow(invalid_flow)
            print("❌ validate_flow should have failed")
            assert False, "Should have failed validation due to multiple node errors"
        except RuleBasedFlowValidationError as e:
            print(f"✅ validate_flow correctly failed with detailed errors")
            
            # Verify enhanced error structure
            assert "nodeErrors" in e.details
            assert len(e.details["nodeErrors"]) > 0
            
            # Check that node errors contain specific node information
            node_errors = e.details["nodeErrors"]
            node_ids = [err["nodeId"] for err in node_errors]
            assert "node-1" in node_ids
            assert "node-2" in node_ids
            assert "node-3" in node_ids
            
            # Test error resource conversion
            error_resource = e.to_error_resource()
            field_names = [fe.field for fe in error_resource.fieldErrors]
            
            # Verify field names include node IDs
            assert any("node.node-1" in field for field in field_names)
            assert any("node.node-2" in field for field in field_names)
            assert any("node.node-3" in field for field in field_names)
            
            print(f"✅ Generated {len(error_resource.fieldErrors)} field-specific errors")

    def test_validate_flow_exact_error_format(self):
        """Test that the error format exactly matches the expected API response structure"""
        print("🧪 Testing exact error format validation...")
        
        service = RuleBasedChatbotService()
        
        # Create flow with both node and edge errors to match your example
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="invalid_question",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="", options=[]),  # Missing text and options
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="invalid_question",
                    target="nonexistent",  # This node doesn't exist
                    sourceHandle="continue",
                    targetHandle="start"
                )
            ]
        )
        
        try:
            service.validate_flow(invalid_flow)
            assert False, "Should have failed validation"
        except RuleBasedFlowValidationError as e:
            # Convert to the format that would be returned by the API
            error_resource = e.to_error_resource()
            
            # Create the exact response format
            response_format = {
                "errorCode": error_resource.errorCode,
                "message": error_resource.message,
                "fieldErrors": [
                    {
                        "field": fe.field,
                        "message": fe.message
                    }
                    for fe in error_resource.fieldErrors
                ]
            }
            
            # Verify the exact structure matches expected format
            assert response_format["errorCode"] == "041120"
            assert response_format["message"] == "Rule-based chatbot flow validation failed"
            assert isinstance(response_format["fieldErrors"], list)
            assert len(response_format["fieldErrors"]) >= 2  # At least 1 node error + 1 edge error
            
            # Verify specific field errors exist
            field_errors = response_format["fieldErrors"]
            field_names = [fe["field"] for fe in field_errors]
            field_messages = [fe["message"] for fe in field_errors]
            
            # Check for node.node-1 errors
            node_fields = [field for field in field_names if field == "node.node-1"]
            assert len(node_fields) >= 1, "Should have at least 1 error for node-1"
            
            # Check for edge.edge-1 error
            edge_fields = [field for field in field_names if field == "edge.edge-1"]
            assert len(edge_fields) >= 1, "Should have at least 1 error for edge-1"
            
            # Verify specific error messages
            text_error_exists = any("must have text" in msg for msg in field_messages)
            edge_error_exists = any("non-existent target node" in msg for msg in field_messages)
            
            assert text_error_exists, "Should have 'must have text' error"
            assert edge_error_exists, "Should have 'non-existent target node' error"
            
            # Verify field error structure
            for fe in field_errors:
                assert "field" in fe, "Each field error must have 'field' key"
                assert "message" in fe, "Each field error must have 'message' key"
                assert isinstance(fe["field"], str), "Field must be string"
                assert isinstance(fe["message"], str), "Message must be string"
            
            print("✅ Exact error format validation passed!")
            print(f"✅ Response has {len(field_errors)} field errors with correct structure")

    def test_validate_flow_node_id_length_limit(self):
        """Test Pydantic validation correctly prevents node IDs exceeding 255 characters"""
        print("🧪 Testing node ID length validation...")
        
        # Create a node ID that exceeds 255 characters
        long_node_id = "a" * 256  # 256 characters - exceeds limit
        
        # Pydantic should prevent creating ChatbotNodeCreate with long ID
        with pytest.raises(ValidationError) as exc_info:
            ChatbotNodeCreate(
                id=long_node_id,
                name="test_node",
                type="question",
                position=NodePosition(x=100, y=100),
                data=NodeData(
                    text="What is your name?",
                    options=[NodeOption(text="Continue", name="continue", position=1)]
                ),
                variableMapping=[]
            )
        
        # Verify Pydantic caught the length error
        error = exc_info.value
        assert "String should have at most 255 characters" in str(error)
        print("✅ Pydantic correctly prevents long node IDs")
    def test_validate_flow_empty_node_id(self):
        """Test Pydantic validation correctly prevents empty node IDs"""
        print("🧪 Testing empty node ID validation...")
        
        # Pydantic should prevent creating ChatbotNodeCreate with empty ID
        with pytest.raises(ValidationError) as exc_info:
            ChatbotNodeCreate(
                id="",  # Empty node ID
                name="test_node",
                type="question",
                position=NodePosition(x=100, y=100),
                data=NodeData(
                    text="What is your name?",
                    options=[NodeOption(text="Continue", name="continue", position=1)]
                ),
                variableMapping=[]
            )
        
        # Verify Pydantic caught the empty ID error
        error = exc_info.value
        assert "Node ID cannot be empty" in str(error)
        print("✅ Pydantic correctly prevents empty node IDs")
    def test_validate_flow_duplicate_node_ids(self):
        """Test duplicate node ID validation within the same flow"""
        print("🧪 Testing duplicate node ID validation...")
        
        service = RuleBasedChatbotService()
        
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="duplicate-id",
                    name="node1",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="Question 1?",
                        options=[NodeOption(text="Next", name="next", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="duplicate-id",  # Same ID as above
                    name="node2",
                    type="sendMessage",
                    position=NodePosition(x=200, y=100),
                    data=NodeData(text="Message 2"),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        try:
            service.validate_flow(invalid_flow)
            assert False, "Should have failed validation due to duplicate node IDs"
        except RuleBasedFlowValidationError as e:
            # Verify the exception has duplicate node ID error
            node_errors = e.details.get("nodeErrors", [])
            duplicate_error_found = any(
                "Duplicate node ID" in err["error"] and "duplicate-id" in err["error"]
                for err in node_errors
            )
            assert duplicate_error_found, "Should have duplicate node ID validation error"
            print("✅ Duplicate node ID validation works correctly")

    def test_node_id_database_constraints_validation(self):
        """Test that the service validation aligns with expected database constraints"""
        print("🧪 Testing alignment with database constraints...")
        
        service = RuleBasedChatbotService()
        
        # Test case 1: Node IDs can be the same across different chatbots (this should be valid)
        chatbot1_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="shared-node-id",
                    name="node1_chatbot1",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="Question for chatbot 1?",
                        options=[NodeOption(text="Next", name="next", position=1)]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        chatbot2_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="shared-node-id",  # Same node ID as chatbot1, but should be allowed
                    name="node1_chatbot2", 
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Message for chatbot 2"),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Both flows should validate successfully since they're for different chatbots
        try:
            service.validate_flow(chatbot1_flow)
            service.validate_flow(chatbot2_flow)
            print("✅ Same node IDs across different chatbots are allowed")
        except RuleBasedFlowValidationError:
            print("❌ Same node IDs across different chatbots should be allowed")
            assert False, "Same node IDs should be allowed across different chatbots"
        
        # Test case 2: 255 character limit (boundary test)
        exactly_255_chars = "a" * 255
        boundary_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id=exactly_255_chars,
                    name="boundary_test",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="Boundary test?",
                        options=[NodeOption(text="Next", name="next", position=1)]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        try:
            service.validate_flow(boundary_flow)
            print("✅ 255-character node ID boundary test passed")
        except RuleBasedFlowValidationError:
            print("❌ 255-character node ID should be allowed")
            assert False, "255-character node ID should be at the valid boundary"
        
        print("✅ Database constraint validation alignment confirmed")

    def test_buttons_node_with_text_header(self):
        """Test buttons node with text format header"""
        print("🧪 Testing buttons node with text header...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with text header
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-1",
                    name="welcome_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Welcome! Choose an option:",
                            "mediaFile": None
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Option 1", position=0),
                            NodeButton(name="btn-1", text="Option 2", position=1),
                            NodeButton(name="btn-2", text="Option 3", position=2)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Text header validation passed")

    def test_buttons_node_with_image_header(self):
        """Test buttons node with image format header"""
        print("🧪 Testing buttons node with image header...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with image header
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-2",
                    name="image_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "image",
                            "text": None,
                            "mediaFile": {
                                "fileId": 1234,
                                "fileSize": 1234,
                                "fileType": "png",
                                "fileName": "welcome.png"
                            }
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Option 1", position=0),
                            NodeButton(name="btn-1", text="Option 2", position=1)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Image header validation passed")

    def test_buttons_node_with_video_header(self):
        """Test buttons node with video format header"""
        print("🧪 Testing buttons node with video header...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with video header
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-3",
                    name="video_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "video",
                            "text": None,
                            "mediaFile": {
                                "fileId": 5678,
                                "fileSize": 2048000,
                                "fileType": "mp4",
                                "fileName": "welcome_video.mp4"
                            }
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Option 1", position=0)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Video header validation passed")

    def test_buttons_node_with_document_header(self):
        """Test buttons node with document format header"""
        print("🧪 Testing buttons node with document header...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with document header
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-4",
                    name="document_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "document",
                            "text": None,
                            "mediaFile": {
                                "fileId": 9999,
                                "fileSize": 512000,
                                "fileType": "pdf",
                                "fileName": "brochure.pdf"
                            }
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Download", position=0)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Document header validation passed")

    def test_buttons_node_string_header_auto_conversion(self):
        """Test buttons node with string header that gets auto-converted"""
        print("🧪 Testing buttons node with string header auto-conversion...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with string header (should be auto-converted)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-5",
                    name="string_header_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header="Welcome! Choose an option:",  # String header
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Option 1", position=0),
                            NodeButton(name="btn-1", text="Option 2", position=1)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully (string gets auto-converted to text format)
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ String header auto-conversion passed")

    def test_buttons_node_missing_buttons_validation_error(self):
        """Test buttons node validation error when buttons are missing"""
        print("🧪 Testing buttons node validation error for missing buttons...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node without buttons (should fail validation)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-6",
                    name="empty_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Welcome! Choose an option:",
                            "mediaFile": None
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[]  # Empty buttons - should cause error
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should fail validation
        with pytest.raises(RuleBasedFlowValidationError):
            service.validate_flow(flow_data)
        print("✅ Missing buttons validation error passed")

    def test_buttons_node_invalid_button_structure_validation_error(self):
        """Test buttons node validation error for invalid button structure"""
        print("🧪 Testing buttons node validation error for invalid button structure...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with invalid button structure (missing name field)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-7",
                    name="invalid_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Welcome! Choose an option:",
                            "mediaFile": None
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="", text="Option 1", position=0),  # Empty name - should cause error
                            NodeButton(name="btn-1", text="", position=1),      # Empty text - should cause error
                            NodeButton(name="btn-2", text="Option 3", position=2)  # Valid position
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should fail validation
        with pytest.raises(RuleBasedFlowValidationError):
            service.validate_flow(flow_data)
        print("✅ Invalid button structure validation error passed")

    def test_buttons_node_missing_mediafile_validation_error(self):
        """Test buttons node with missing mediaFile (should still validate)"""
        print("🧪 Testing buttons node with missing mediaFile...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with image format but no mediaFile (should still validate)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-8",
                    name="missing_mediafile",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "image",
                            "text": None,
                            "mediaFile": None  # Missing mediaFile for image format
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Option 1", position=0)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully (missing mediaFile is allowed)
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Missing mediaFile validation passed")

    def test_buttons_node_invalid_mediafile_structure_validation_error(self):
        """Test buttons node with valid mediaFile structure"""
        print("🧪 Testing buttons node with valid mediaFile structure...")
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with valid mediaFile structure
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-9",
                    name="valid_mediafile",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "image",
                            "text": None,
                            "mediaFile": {
                                "fileId": 1234,
                                "fileSize": 1234,
                                "fileType": "png",
                                "fileName": "image.png"  # Valid structure
                            }
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-0", text="Option 1", position=0)
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Valid mediaFile structure validation passed")

    def test_buttons_node_mixed_header_formats(self):
        """Test multiple buttons nodes with different header formats"""
        print("🧪 Testing multiple buttons nodes with different header formats...")
        
        service = RuleBasedChatbotService()
        
        # Create flow with multiple buttons nodes using different header formats
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                # Text header
                ChatbotNodeCreate(
                    id="node-text-buttons",
                    name="text_buttons",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Text Header",
                            "mediaFile": None
                        },
                        body="Text body",
                        buttons=[NodeButton(name="btn-1", text="Option 1", position=0)]
                    ),
                    variableMapping=[]
                ),
                # Image header
                ChatbotNodeCreate(
                    id="node-image-buttons",
                    name="image_buttons",
                    type="buttons",
                    position=NodePosition(x=200, y=100),
                    data=NodeData(
                        header={
                            "format": "image",
                            "text": None,
                            "mediaFile": {
                                "fileId": 1234,
                                "fileSize": 1234,
                                "fileType": "png",
                                "fileName": "image.png"
                            }
                        },
                        body="Image body",
                        buttons=[NodeButton(name="btn-2", text="Option 2", position=0)]
                    ),
                    variableMapping=[]
                ),
                # Video header
                ChatbotNodeCreate(
                    id="node-video-buttons",
                    name="video_buttons",
                    type="buttons",
                    position=NodePosition(x=300, y=100),
                    data=NodeData(
                        header={
                            "format": "video",
                            "text": None,
                            "mediaFile": {
                                "fileId": 5678,
                                "fileSize": 2048000,
                                "fileType": "mp4",
                                "fileName": "video.mp4"
                            }
                        },
                        body="Video body",
                        buttons=[NodeButton(name="btn-3", text="Option 3", position=0)]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        assert validation_result["summary"]["node_types"]["buttons"] == 3
        print("✅ Mixed header formats validation passed")

    def test_url_node_basic_validation(self):
        """Test basic URL node validation"""
        print("🧪 Testing basic URL node validation...")

        service = RuleBasedChatbotService()

        # Create URL node with required fields
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-url-1",
                    name="basic_url",
                    type="url",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="https://example.com"
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )

        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ Basic URL node validation passed")

    def test_url_node_with_entity_fields(self):
        """Test URL node with entity field mappings"""
        print("🧪 Testing URL node with entity fields...")

        service = RuleBasedChatbotService()

        # Create URL node with entity fields
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-url-2",
                    name="url_with_entities",
                    type="url",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="https://example.com/user/{{firstName}}",
                        entityFields=[
                            {
                                "id": "field-1",
                                "entityType": "LEAD",
                                "fieldId": 129273,
                                "standard": True,
                                "displayName": "First Name",
                                "name": "firstName"
                            }
                        ]
                    ),
                    variableMapping=[
                        {
                            "id": "1",
                            "componentType": "BODY",
                            "variable": "1",
                            "entity": "lead",
                            "internalName": "firstName",
                            "fallbackValue": "",
                            "fieldType": "TEXT_FIELD"
                        }
                    ]
                )
            ],
            edges=[]
        )

        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ URL node with entity fields validation passed")

    def test_url_node_missing_button_text_validation_error(self):
        """Test URL node validation error when buttonText is missing"""
        print("🧪 Testing URL node validation error for missing buttonText...")

        service = RuleBasedChatbotService()

        # Create URL node without buttonText (should fail validation)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-url-3",
                    name="missing_button_text",
                    type="url",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        # buttonText missing - should cause error
                        ctaURL="https://example.com"
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )

        # Should fail validation
        with pytest.raises(RuleBasedFlowValidationError):
            service.validate_flow(flow_data)
        print("✅ Missing buttonText validation error passed")

    def test_url_node_missing_cta_url_validation_error(self):
        """Test URL node validation error when ctaURL is missing"""
        print("🧪 Testing URL node validation error for missing ctaURL...")

        service = RuleBasedChatbotService()

        # Create URL node without ctaURL (should fail validation)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-url-4",
                    name="missing_cta_url",
                    type="url",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website"
                        # ctaURL missing - should cause error
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )

        # Should fail validation
        with pytest.raises(RuleBasedFlowValidationError):
            service.validate_flow(flow_data)
        print("✅ Missing ctaURL validation error passed")

    def test_url_node_invalid_url_format_validation_error(self):
        """Test URL node validation error for invalid URL format"""
        print("🧪 Testing URL node validation error for invalid URL format...")

        service = RuleBasedChatbotService()

        # Create URL node with invalid URL format (should fail validation)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-url-5",
                    name="invalid_url_format",
                    type="url",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="invalid-url-format"  # Invalid URL - should cause error
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )

        # Should fail validation
        with pytest.raises(RuleBasedFlowValidationError):
            service.validate_flow(flow_data)
        print("✅ Invalid URL format validation error passed")

    def test_url_node_chaining_validation(self):
        """Test URL node chaining with other node types"""
        print("🧪 Testing URL node chaining validation...")

        service = RuleBasedChatbotService()

        # Create flow with URL node chaining: sendMessage -> URL -> question
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="send_node",
                    name="send_message",
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    isFirstNode=True,
                    data=NodeData(
                        text="Welcome! Here's some information."
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="url_node",
                    name="url_node",
                    type="url",
                    position=NodePosition(x=200, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="https://example.com"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="question_node",
                    name="question_node",
                    type="question",
                    position=NodePosition(x=300, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        text="What's your name?"
                    ),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge1",
                    source="send_message",
                    target="url_node",
                    sourceHandle=None,
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge2",
                    source="url_node",
                    target="question_node",
                    sourceHandle=None,
                    targetHandle=None
                )
            ]
        )

        # Should validate successfully (no loops, valid chaining)
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ URL node chaining validation passed")

    def test_url_node_loop_detection_safe(self):
        """Test URL node in safe loop (contains user input nodes)"""
        print("🧪 Testing URL node in safe loop...")

        service = RuleBasedChatbotService()

        # Create flow with safe loop: question -> URL -> question (safe because both require user interaction)
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="question1",
                    name="question1",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    isFirstNode=True,
                    data=NodeData(
                        text="Do you want to visit our website?",
                        options=[
                            {"id": "yes", "text": "Yes"},
                            {"id": "no", "text": "No"}
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="url_node",
                    name="url_node",
                    type="url",
                    position=NodePosition(x=200, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="https://example.com"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="question2",
                    name="question2",
                    type="question",
                    position=NodePosition(x=300, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        text="Would you like to visit again?",
                        options=[
                            {"id": "yes", "text": "Yes"},
                            {"id": "no", "text": "No"}
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge1",
                    source="question1",
                    target="url_node",
                    sourceHandle="yes",
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge2",
                    source="url_node",
                    target="question2",
                    sourceHandle=None,
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge3",
                    source="question2",
                    target="question1",
                    sourceHandle="yes",
                    targetHandle=None
                )
            ]
        )

        # Should validate successfully (safe loop with user input nodes)
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ URL node safe loop validation passed")

    def test_url_node_infinite_loop_detection(self):
        """Test URL node in potential infinite loop with sendMessage"""
        print("🧪 Testing URL node infinite loop detection...")

        service = RuleBasedChatbotService()

        # Create flow with potential infinite loop: sendMessage -> URL -> sendMessage
        # This should be flagged as potentially problematic since sendMessage auto-advances
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="send1",
                    name="send1",
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    isFirstNode=True,
                    data=NodeData(
                        text="Welcome message"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="url_node",
                    name="url_node",
                    type="url",
                    position=NodePosition(x=200, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="https://example.com"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="send2",
                    name="send2",
                    type="sendMessage",
                    position=NodePosition(x=300, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        text="Thank you for visiting!"
                    ),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge1",
                    source="send1",
                    target="url_node",
                    sourceHandle=None,
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge2",
                    source="url_node",
                    target="send2",
                    sourceHandle=None,
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge3",
                    source="send2",
                    target="send1",
                    sourceHandle=None,
                    targetHandle=None
                )
            ]
        )

        # This should still validate successfully because URL nodes require user interaction
        # (clicking the button), which breaks the infinite loop
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ URL node with sendMessage loop validation passed (safe due to user interaction)")

    def test_url_node_terminal_behavior(self):
        """Test URL node as terminal node (no outgoing edges)"""
        print("🧪 Testing URL node terminal behavior...")

        service = RuleBasedChatbotService()

        # Create flow with URL node as terminal node
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="question_node",
                    name="question_node",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    isFirstNode=True,
                    data=NodeData(
                        text="Would you like to visit our website?",
                        options=[
                            {"id": "yes", "text": "Yes"},
                            {"id": "no", "text": "No"}
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="url_terminal",
                    name="url_terminal",
                    type="url",
                    position=NodePosition(x=200, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Visit our website",
                            "mediaFile": None
                        },
                        body="Click the button below to visit our website. This will end our conversation.",
                        footer="Thank you for your interest",
                        buttonText="Visit Website",
                        ctaURL="https://example.com"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="send_no",
                    name="send_no",
                    type="sendMessage",
                    position=NodePosition(x=200, y=200),
                    isFirstNode=False,
                    data=NodeData(
                        text="No problem! Have a great day."
                    ),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge1",
                    source="question_node",
                    target="url_terminal",
                    sourceHandle="yes",
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge2",
                    source="question_node",
                    target="send_no",
                    sourceHandle="no",
                    targetHandle=None
                )
            ]
        )

        # Should validate successfully (URL node as terminal is valid)
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ URL node terminal behavior validation passed")

    def test_url_node_complex_chaining(self):
        """Test complex chaining scenario with URL nodes"""
        print("🧪 Testing complex URL node chaining...")

        service = RuleBasedChatbotService()

        # Create complex flow: sendMessage -> URL -> buttons -> URL -> question
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="welcome",
                    name="welcome",
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    isFirstNode=True,
                    data=NodeData(
                        text="Welcome to our service!"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="url1",
                    name="url1",
                    type="url",
                    position=NodePosition(x=200, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Learn more about us",
                            "mediaFile": None
                        },
                        body="Visit our about page",
                        buttonText="Learn More",
                        ctaURL="https://example.com/about"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="buttons",
                    name="buttons",
                    type="buttons",
                    position=NodePosition(x=300, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        body="What would you like to do next?",
                        buttons=[
                            {"id": "contact", "text": "Contact Us"},
                            {"id": "products", "text": "View Products"}
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="url2",
                    name="url2",
                    type="url",
                    position=NodePosition(x=400, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Contact us",
                            "mediaFile": None
                        },
                        body="Visit our contact page",
                        buttonText="Contact Us",
                        ctaURL="https://example.com/contact"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="final_question",
                    name="final_question",
                    type="question",
                    position=NodePosition(x=500, y=100),
                    isFirstNode=False,
                    data=NodeData(
                        text="How did you hear about us?"
                    ),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge1",
                    source="welcome",
                    target="url1",
                    sourceHandle=None,
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge2",
                    source="url1",
                    target="buttons",
                    sourceHandle=None,
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge3",
                    source="buttons",
                    target="url2",
                    sourceHandle="contact",
                    targetHandle=None
                ),
                ChatbotEdgeCreate(
                    id="edge4",
                    source="url2",
                    target="final_question",
                    sourceHandle=None,
                    targetHandle=None
                )
            ]
        )

        # Should validate successfully (complex but valid chaining)
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ Complex URL node chaining validation passed")

    def test_list_node_basic_validation(self):
        """Test basic list node validation"""
        service = RuleBasedChatbotService()
        
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node_1",
                    name="node_1",
                    type="list",
                    position=NodePosition(x=0, y=0),
                    data=NodeData(
                        header=NodeHeader(format="text", text="Choose a package"),
                        body="Please select one of the following packages:",
                        footer="Thank you for your selection",
                        menuButton="View All",
                        sections=[
                            NodeSection(
                                title="Premium Packages",
                                rows=[
                                    {
                                        "id": "premium-basic",
                                        "title": "Premium Basic",
                                        "description": "Basic premium features"
                                    },
                                    {
                                        "id": "premium-pro",
                                        "title": "Premium Pro",
                                        "description": "Advanced premium features"
                                    }
                                ]
                            )
                        ]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ Basic list node validation passed")

    def test_list_node_with_variable_mapping_validation(self):
        """Test list node with variable mapping in header and body"""
        service = RuleBasedChatbotService()
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "isFirstNode": True,
                    "data": {
                        "header": "Hello {{1}}, choose a package",
                        "body": "Hi {{1}}, please select one of the following packages:",
                        "footer": "Thank you {{1}} for your selection",
                        "menuButton": "View All",
                        "sections": [
                            {
                                "title": "Packages for {{1}}",
                                "rows": [
                                    {
                                        "id": "package-{{1}}",
                                        "title": "Package for {{1}}",
                                        "description": "Custom package for {{1}}"
                                    }
                                ]
                            }
                        ]
                    },
                    "variableMapping": [
                        {
                            "entity": "contact",
                            "internalName": "firstName",
                            "variable": "1",
                            "fallbackValue": "User"
                        }
                    ]
                }
            ],
            "edges": []
        }
        
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ List node with variable mapping validation passed")

    def test_list_node_with_entity_fields_validation(self):
        """Test list node with entity fields for updates"""
        service = RuleBasedChatbotService()
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "isFirstNode": True,
                    "data": {
                        "header": "Choose your package",
                        "body": "Please select a package:",
                        "footer": "Thank you",
                        "menuButton": "View All",
                        "sections": [
                            {
                                "title": "Available Packages",
                                "rows": [
                                    {
                                        "id": "basic",
                                        "title": "Basic Package",
                                        "description": "Basic features"
                                    },
                                    {
                                        "id": "premium",
                                        "title": "Premium Package",
                                        "description": "Premium features"
                                    }
                                ]
                            }
                        ],
                        "entityFields": [
                            {
                                "name": "package_type",
                                "entityType": "CONTACT",
                                "fieldId": 123
                            },
                            {
                                "name": "subscription_level",
                                "entityType": "LEAD",
                                "fieldId": 456
                            }
                        ]
                    }
                }
            ],
            "edges": []
        }
        
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        print("✅ List node with entity fields validation passed")

    def test_list_node_missing_sections_validation_error(self):
        """Test list node validation error when sections are missing"""
        service = RuleBasedChatbotService()
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "isFirstNode": True,
                    "data": {
                        "header": "Choose a package",
                        "body": "Please select one:",
                        "footer": "Thank you",
                        "menuButton": "View All"
                        # Missing sections
                    }
                }
            ],
            "edges": []
        }
        
        with pytest.raises(RuleBasedFlowValidationError) as exc_info:
            service.validate_flow(flow_data)
        
        error_message = str(exc_info.value)
        assert "sections" in error_message.lower()
        print("✅ List node missing sections validation error passed")

    def test_list_node_empty_sections_validation_error(self):
        """Test list node validation error when sections are empty"""
        service = RuleBasedChatbotService()
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "isFirstNode": True,
                    "data": {
                        "header": "Choose a package",
                        "body": "Please select one:",
                        "footer": "Thank you",
                        "menuButton": "View All",
                        "sections": []  # Empty sections
                    }
                }
            ],
            "edges": []
        }
        
        with pytest.raises(RuleBasedFlowValidationError) as exc_info:
            service.validate_flow(flow_data)
        
        error_message = str(exc_info.value)
        assert "sections" in error_message.lower()
        print("✅ List node empty sections validation error passed")

    def test_list_node_invalid_row_structure_validation_error(self):
        """Test list node validation error for invalid row structure"""
        service = RuleBasedChatbotService()
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "isFirstNode": True,
                    "data": {
                        "header": "Choose a package",
                        "body": "Please select one:",
                        "footer": "Thank you",
                        "menuButton": "View All",
                        "sections": [
                            {
                                "title": "Packages",
                                "rows": [
                                    {
                                        "id": "package-1",
                                        "title": "Package 1",
                                        "description": "Description 1"
                                    },
                                    {
                                        "id": "",  # Empty ID
                                        "title": "",  # Empty title
                                        "description": ""  # Empty description
                                    }
                                ]
                            }
                        ]
                    }
                }
            ],
            "edges": []
        }
        
        with pytest.raises(RuleBasedFlowValidationError) as exc_info:
            service.validate_flow(flow_data)
        
        error_message = str(exc_info.value)
        assert "row" in error_message.lower() or "title" in error_message.lower()
        print("✅ List node invalid row structure validation error passed")
    
    def test_list_node_max_10_sections_validation(self):
        """Test that list node with more than 10 sections fails validation"""
        print("\n🧪 Testing list node max 10 sections validation...")
        
        from app.services.rule_based_chatbot_service import RuleBasedChatbotService
        from app.models import RuleBasedChatbotFlow
        from app.exceptions import RuleBasedFlowValidationError
        
        service = RuleBasedChatbotService()
        
        # Create list node with 11 sections (exceeds max)
        sections = []
        for i in range(11):  # 11 sections - exceeds max of 10
            sections.append({
                "id": f"section-{i}",
                "title": f"Section {i+1}",
                "rows": [
                    {"id": f"section-{i}-row-0", "text": "Row 1", "position": 0}
                ],
                "position": i
            })
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "header": {"format": "text", "text": "Choose"},
                        "body": "Select option",
                        "footer": "Footer",
                        "menuButton": "Menu",
                        "sections": sections
                    },
                    "isFirstNode": True,
                    "variableMapping": []
                }
            ],
            "edges": []
        }
        
        flow_data = RuleBasedChatbotFlow(**flow_data)
        
        with pytest.raises(RuleBasedFlowValidationError) as exc_info:
            service.validate_flow(flow_data)
        
        error_message = str(exc_info.value)
        assert "maximum 10 sections" in error_message
        assert "found 11" in error_message
        print("✅ List node max 10 sections validation passed")
    
    def test_list_node_max_10_total_rows_validation(self):
        """Test that list node with more than 10 total rows fails validation"""
        print("\n🧪 Testing list node max 10 total rows validation...")
        
        from app.services.rule_based_chatbot_service import RuleBasedChatbotService
        from app.models import RuleBasedChatbotFlow
        from app.exceptions import RuleBasedFlowValidationError
        
        service = RuleBasedChatbotService()
        
        # Create list node with 11 total rows across 3 sections (exceeds max)
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "header": {"format": "text", "text": "Choose"},
                        "body": "Select option",
                        "footer": "Footer",
                        "menuButton": "Menu",
                        "sections": [
                            {
                                "id": "section-0",
                                "title": "Section 1",
                                "rows": [
                                    {"id": f"section-0-row-{j}", "text": f"Row {j+1}", "position": j}
                                    for j in range(5)  # 5 rows
                                ],
                                "position": 0
                            },
                            {
                                "id": "section-1",
                                "title": "Section 2",
                                "rows": [
                                    {"id": f"section-1-row-{j}", "text": f"Row {j+1}", "position": j}
                                    for j in range(4)  # 4 rows
                                ],
                                "position": 1
                            },
                            {
                                "id": "section-2",
                                "title": "Section 3",
                                "rows": [
                                    {"id": f"section-2-row-{j}", "text": f"Row {j+1}", "position": j}
                                    for j in range(2)  # 2 rows
                                ],
                                "position": 2
                            }
                        ]  # Total: 5 + 4 + 2 = 11 rows (exceeds max of 10)
                    },
                    "isFirstNode": True,
                    "variableMapping": []
                }
            ],
            "edges": []
        }
        
        flow_data = RuleBasedChatbotFlow(**flow_data)
        
        with pytest.raises(RuleBasedFlowValidationError) as exc_info:
            service.validate_flow(flow_data)
        
        error_message = str(exc_info.value)
        assert "maximum 10 rows" in error_message
        assert "found 11" in error_message
        print("✅ List node max 10 total rows validation passed")
    
    def test_list_node_with_exactly_10_sections_passes(self):
        """Test that list node with exactly 10 sections passes validation"""
        print("\n🧪 Testing list node with exactly 10 sections...")
        
        from app.services.rule_based_chatbot_service import RuleBasedChatbotService
        from app.models import RuleBasedChatbotFlow
        
        service = RuleBasedChatbotService()
        
        # Create list node with exactly 10 sections
        sections = []
        for i in range(10):  # Exactly 10 sections
            sections.append({
                "id": f"section-{i}",
                "title": f"Section {i+1}",
                "rows": [
                    {"id": f"section-{i}-row-0", "text": "Row 1", "position": 0}
                ],
                "position": i
            })
        
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "header": {"format": "text", "text": "Choose"},
                        "body": "Select option",
                        "footer": "Footer",
                        "menuButton": "Menu",
                        "sections": sections
                    },
                    "isFirstNode": True,
                    "variableMapping": []
                }
            ],
            "edges": []
        }
        
        flow_data = RuleBasedChatbotFlow(**flow_data)
        
        # Should not raise exception
        result = service.validate_flow(flow_data)
        assert result["valid"] == True
        print("✅ List node with exactly 10 sections passes validation")
    
    def test_list_node_with_exactly_10_total_rows_passes(self):
        """Test that list node with exactly 10 total rows passes validation"""
        print("\n🧪 Testing list node with exactly 10 total rows...")
        
        from app.services.rule_based_chatbot_service import RuleBasedChatbotService
        from app.models import RuleBasedChatbotFlow
        
        service = RuleBasedChatbotService()
        
        # Create list node with exactly 10 total rows
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "list",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "header": {"format": "text", "text": "Choose"},
                        "body": "Select option",
                        "footer": "Footer",
                        "menuButton": "Menu",
                        "sections": [
                            {
                                "id": "section-0",
                                "title": "Section 1",
                                "rows": [
                                    {"id": f"section-0-row-{j}", "text": f"Row {j+1}", "position": j}
                                    for j in range(6)  # 6 rows
                                ],
                                "position": 0
                            },
                            {
                                "id": "section-1",
                                "title": "Section 2",
                                "rows": [
                                    {"id": f"section-1-row-{j}", "text": f"Row {j+1}", "position": j}
                                    for j in range(4)  # 4 rows
                                ],
                                "position": 1
                            }
                        ]  # Total: 6 + 4 = 10 rows (exactly at limit)
                    },
                    "isFirstNode": True,
                    "variableMapping": []
                }
            ],
            "edges": []
        }
        
        flow_data = RuleBasedChatbotFlow(**flow_data)
        
        # Should not raise exception
        result = service.validate_flow(flow_data)
        assert result["valid"] == True
        print("✅ List node with exactly 10 total rows passes validation")
    
    def test_buttons_node_max_3_buttons_validation(self):
        """Test that buttons node with more than 3 buttons fails validation"""
        print("\n🧪 Testing buttons node max 3 buttons validation...")
        
        from app.services.rule_based_chatbot_service import RuleBasedChatbotService
        from app.models import RuleBasedChatbotFlow
        from app.exceptions import RuleBasedFlowValidationError
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with 4 buttons (exceeds max of 3)
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "buttons",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "text": "Choose an option:",
                        "buttons": [
                            {"name": "button-0", "text": "Option 1", "position": 0},
                            {"name": "button-1", "text": "Option 2", "position": 1},
                            {"name": "button-2", "text": "Option 3", "position": 2},
                            {"name": "button-3", "text": "Option 4", "position": 3}  # 4th button - exceeds max
                        ]
                    },
                    "isFirstNode": True,
                    "variableMapping": []
                }
            ],
            "edges": []
        }
        
        flow_data = RuleBasedChatbotFlow(**flow_data)
        
        with pytest.raises(RuleBasedFlowValidationError) as exc_info:
            service.validate_flow(flow_data)
        
        error_message = str(exc_info.value)
        assert "maximum 3 buttons" in error_message
        assert "found 4" in error_message
        print("✅ Buttons node max 3 buttons validation passed")
    
    def test_buttons_node_with_exactly_3_buttons_passes(self):
        """Test that buttons node with exactly 3 buttons passes validation"""
        print("\n🧪 Testing buttons node with exactly 3 buttons...")
        
        from app.services.rule_based_chatbot_service import RuleBasedChatbotService
        from app.models import RuleBasedChatbotFlow
        
        service = RuleBasedChatbotService()
        
        # Create buttons node with exactly 3 buttons
        flow_data = {
            "nodes": [
                {
                    "id": "node_1",
                    "name": "node_1",
                    "type": "buttons",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "text": "Choose an option:",
                        "buttons": [
                            {"name": "button-0", "text": "Option 1", "position": 0},
                            {"name": "button-1", "text": "Option 2", "position": 1},
                            {"name": "button-2", "text": "Option 3", "position": 2}
                        ]
                    },
                    "isFirstNode": True,
                    "variableMapping": []
                }
            ],
            "edges": []
        }
        
        flow_data = RuleBasedChatbotFlow(**flow_data)
        
        # Should not raise exception
        result = service.validate_flow(flow_data)
        assert result["valid"] == True
        print("✅ Buttons node with exactly 3 buttons passes validation")


class TestRuleBasedChatbotIntegration:
    """Integration tests for rule-based chatbot functionality"""
    
    def setup_method(self):
        """Setup test data"""
        self.service = RuleBasedChatbotService()
        self.tenant_id = 12345
        self.chatbot_id = "chatbot-456"

    def test_complete_flow_validation_and_creation(self):
        """Test complete flow from validation to creation"""
        # Complex flow with multiple node types
        complex_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="welcome",
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Welcome! Let's get started."),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="ask_name",
                    type="question",
                    position=NodePosition(x=100, y=200),
                    data=NodeData(
                        text="What is your name?",
                        options=[NodeOption(text="Continue", name="continue", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-3",
                    name="ask_email",
                    type="question",
                    position=NodePosition(x=100, y=300),
                    data=NodeData(
                        text="What is your email?",
                        options=[NodeOption(text="Continue", name="continue", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-4",
                    name="show_buttons",
                    type="button",
                    position=NodePosition(x=100, y=400),
                    data=NodeData(
                        buttons=[
                            NodeButton(name="yes", text="Yes", position=1),
                            NodeButton(name="no", text="No", position=2)
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-5",
                    name="show_list",
                    type="list",
                    position=NodePosition(x=100, y=500),
                    data=NodeData(
                        sections=[
                            NodeSection(
                                title="Options",
                                rows=[{"id": "1", "text": "Option 1"}]
                            )
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-6",
                    name="condition",
                    type="condition",
                    position=NodePosition(x=100, y=600),
                    data=NodeData(
                        conditions=[{"field": "name", "operator": "equals", "value": "John"}],
                        operator="AND"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-7",
                    name="thank_you",
                    type="sendMessage",
                    position=NodePosition(x=300, y=400),
                    data=NodeData(text="Thank you for your responses!"),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="welcome",
                    sourceHandle="default",
                    target="ask_name",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-2",
                    source="ask_name",
                    sourceHandle="continue",
                    target="ask_email",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-3",
                    source="ask_email",
                    sourceHandle="continue",
                    target="show_buttons",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-4",
                    source="show_buttons",
                    sourceHandle="yes",
                    target="show_list",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-5",
                    source="show_list",
                    sourceHandle="default",
                    target="condition",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-6",
                    source="condition",
                    sourceHandle="true",
                    target="thank_you",
                    targetHandle="start"
                )
            ]
        )
        
        # Test validation
        validation_result = self.service.validate_flow(complex_flow)
        
        assert validation_result["valid"] is True
        assert validation_result["summary"]["total_nodes"] == 7
        assert validation_result["summary"]["total_edges"] == 6
        assert "question" in validation_result["summary"]["node_types"]
        assert validation_result["summary"]["node_types"]["question"] == 2
        assert validation_result["summary"]["node_types"]["sendMessage"] == 2
        assert validation_result["summary"]["node_types"]["button"] == 1
        assert validation_result["summary"]["node_types"]["list"] == 1
        assert validation_result["summary"]["node_types"]["condition"] == 1

    def test_flow_with_entity_fields(self):
        """Test flow with entity field mappings"""
        flow_with_entities = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="collect_lead_info",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="What is your first name?",
                        options=[NodeOption(text="Continue", name="continue", position=1)],
                        entityFields=[
                            {
                                "id": "field-1",
                                "entityType": "Lead",
                                "fieldId": 1,
                                "name": "firstName",
                                "displayName": "First Name",
                                "standard": True
                            }
                        ]
                    ),
                    variableMapping=[
                        {
                            "id": "1",
                            "componentType": "BODY",
                            "variable": "1",
                            "entity": "lead",
                            "internalName": "firstName",
                            "fallbackValue": "",
                            "fieldType": "TEXT_FIELD"
                        }
                    ]
                )
            ],
            edges=[]
        )
        
        # Test validation - should not raise exception for valid flow
        try:
            self.service.validate_flow(flow_with_entities)
            # If we get here, validation passed (no exception raised)
            success = True
        except RuleBasedFlowValidationError as e:
            print(f"❌ Validation failed: {e}")
            print(f"❌ Details: {e.details}")
            success = False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            success = False
        
        assert success, "Valid flow with entity fields should not raise validation exceptions"

    def test_node_position_float_values(self):
        """Test that NodePosition accepts and handles float values correctly"""
        # Test with float values
        position = NodePosition(x=100.12, y=221.67)
        assert position.x == 100.12
        assert position.y == 221.67
        assert isinstance(position.x, float)
        assert isinstance(position.y, float)
        
        # Test with integer values (should still work and be converted to float)
        position_int = NodePosition(x=100, y=200)
        assert position_int.x == 100.0
        assert position_int.y == 200.0
        assert isinstance(position_int.x, float)
        assert isinstance(position_int.y, float)
        
        # Test JSON serialization
        position_dict = position.model_dump()
        assert position_dict["x"] == 100.12
        assert position_dict["y"] == 221.67
        
        # Test JSON deserialization
        position_from_dict = NodePosition(**position_dict)
        assert position_from_dict.x == 100.12
        assert position_from_dict.y == 221.67

    def test_chatbot_node_with_float_positions(self):
        """Test that ChatbotNode can handle float position values"""
        from datetime import datetime
        
        # Test creating a ChatbotNodeResponse with float positions
        response = ChatbotNodeResponse(
            id="test-id",
            node_id="test-node",
            name="test-node",
            type="question",
            position_x=100.12,
            position_y=221.67,
            data={"text": "Test question"},
            variable_mapping=[],
            entity_fields=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert response.position_x == 100.12
        assert response.position_y == 221.67
        assert isinstance(response.position_x, float)
        assert isinstance(response.position_y, float)
        
        # Test JSON serialization
        response_dict = response.model_dump()
        assert response_dict["position_x"] == 100.12
        assert response_dict["position_y"] == 221.67

    def test_rule_based_flow_with_float_positions(self):
        """Test that rule-based chatbot flows work with float positions"""
        flow_data = {
            "nodes": [
                {
                    "id": "123333-1",
                    "componentType": "question",
                    "variable": "user_name",
                    "entity": "contact",
                    "internalName": "name",
                    "fallbackValue": "User",
                    "fieldType": "text",
                    "position": {"x": 100.12, "y": 221.67},
                    "data": {
                        "text": "What's your name?",
                        "options": []
                    }
                },
                {
                    "id": "123333-2", 
                    "componentType": "sendMessage",
                    "variable": "",
                    "entity": "",
                    "internalName": "",
                    "fallbackValue": "",
                    "fieldType": "",
                    "position": {"x": 300.45, "y": 150.89},
                    "data": {
                        "text": "Hello {{user_name}}!"
                    }
                }
            ],
            "edges": [
                {
                    "source": "123333-1",
                    "target": "123333-2"
                }
            ]
        }
        
        # Test that the flow data can be parsed correctly
        try:
            # Validate that NodePosition can be created from the flow data
            for node_data in flow_data["nodes"]:
                position = NodePosition(**node_data["position"])
                assert isinstance(position.x, float)
                assert isinstance(position.y, float)
                assert position.x == node_data["position"]["x"]
                assert position.y == node_data["position"]["y"]
            
            success = True
        except Exception as e:
            print(f"❌ Error processing float positions: {e}")
            success = False
        
        assert success, "Rule-based flow should handle float positions correctly"


# Tests for recent changes
def test_question_node_without_options_passes_validation():
    """Test that question nodes without options pass validation"""
    print("🧪 Testing question node without options validation...")
    
    service = RuleBasedChatbotService()
    
    # Create a question node without options
    question_node_data = Mock()
    question_node_data.id = "q1"
    question_node_data.name = "Question without options"
    question_node_data.type = "question"
    question_node_data.data = Mock()
    question_node_data.data.text = "What is your name?"
    question_node_data.data.options = None  # No options
    question_node_data.variableMapping = []
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [question_node_data]
    flow_data.edges = []
    
    # This should not raise a validation error
    try:
        service.validate_flow(flow_data)
        print("✅ Question node without options passed validation")
        assert True
    except RuleBasedFlowValidationError as e:
        # Check that the error is not about missing options
        assert "options" not in str(e).lower()
        print(f"❌ Unexpected validation error: {e}")


def test_question_node_with_empty_options_passes_validation():
    """Test that question nodes with empty options array pass validation"""
    print("🧪 Testing question node with empty options validation...")
    
    service = RuleBasedChatbotService()
    
    # Create a question node with empty options
    question_node_data = Mock()
    question_node_data.id = "q1"
    question_node_data.name = "Question with empty options"
    question_node_data.type = "question"
    question_node_data.data = Mock()
    question_node_data.data.text = "What is your name?"
    question_node_data.data.options = []  # Empty options array
    question_node_data.variableMapping = []
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [question_node_data]
    flow_data.edges = []
    
    # This should not raise a validation error
    try:
        service.validate_flow(flow_data)
        print("✅ Question node with empty options passed validation")
        assert True
    except RuleBasedFlowValidationError as e:
        # Check that the error is not about missing options
        assert "options" not in str(e).lower()
        print(f"❌ Unexpected validation error: {e}")


def test_question_node_still_requires_text():
    """Test that question nodes still require text field"""
    print("🧪 Testing question node still requires text...")
    
    service = RuleBasedChatbotService()
    
    # Create a question node without text
    question_node_data = Mock()
    question_node_data.id = "q1"
    question_node_data.name = "Question without text"
    question_node_data.type = "question"
    question_node_data.data = Mock()
    question_node_data.data.text = None  # No text
    question_node_data.data.options = []
    question_node_data.variableMapping = []
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [question_node_data]
    flow_data.edges = []
    
    # This should raise a validation error about missing text
    try:
        service.validate_flow(flow_data)
        print("❌ Should have failed validation for missing text")
        assert False
    except RuleBasedFlowValidationError as e:
        # Check that the validation errors contain text-related error
        validation_errors = e.details.get("validationErrors", [])
        text_error_found = any("text" in error.lower() for error in validation_errors)
        assert text_error_found, f"Expected text-related error in validation errors: {validation_errors}"
        print("✅ Question node correctly requires text field")


def test_variable_mapping_without_id_passes_validation():
    """Test that variable mappings without id field pass validation"""
    print("🧪 Testing variable mapping without id field...")
    
    service = RuleBasedChatbotService()
    
    # Create a node with variable mapping without id
    node_data = Mock()
    node_data.id = "n1"
    node_data.name = "Node with variable mapping"
    node_data.type = "question"
    node_data.data = Mock()
    node_data.data.text = "Hello {{1}}"
    node_data.data.options = []
    
    # Variable mapping without id field
    node_data.variableMapping = [
        {
            "componentType": "BODY",
            "variable": "1",
            "entity": "contact",
            "internalName": "firstName",
            "fallbackValue": "",
            "fieldType": "TEXT_FIELD"
            # No "id" field
        }
    ]
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [node_data]
    flow_data.edges = []
    
    # This should not raise a validation error
    try:
        service.validate_flow(flow_data)
        print("✅ Variable mapping without id field passed validation")
        assert True
    except RuleBasedFlowValidationError as e:
        # Check that the error is not about missing id
        assert "id" not in str(e).lower()
        print(f"❌ Unexpected validation error: {e}")


def test_variable_mapping_still_requires_other_fields():
    """Test that variable mappings still require other essential fields"""
    print("🧪 Testing variable mapping still requires other fields...")
    
    service = RuleBasedChatbotService()
    
    # Create a node with incomplete variable mapping
    node_data = Mock()
    node_data.id = "n1"
    node_data.name = "Node with incomplete variable mapping"
    node_data.type = "question"
    node_data.data = Mock()
    node_data.data.text = "Hello {{1}}"
    node_data.data.options = []
    
    # Variable mapping missing required fields
    node_data.variableMapping = [
        {
            "componentType": "BODY",
            "variable": "1"
            # Missing: entity, internalName, fallbackValue, fieldType
        }
    ]
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [node_data]
    flow_data.edges = []
    
    # This should raise a validation error about missing fields
    try:
        service.validate_flow(flow_data)
        print("❌ Should have failed validation for missing fields")
        assert False
    except RuleBasedFlowValidationError as e:
        # Check that the validation errors contain field-related errors
        validation_errors = e.details.get("validationErrors", [])
        field_error_found = any(
            any(field in error.lower() for field in ["entity", "internalname", "fallbackvalue", "fieldtype"])
            for error in validation_errors
        )
        assert field_error_found, f"Expected field-related error in validation errors: {validation_errors}"
        print("✅ Variable mapping correctly requires essential fields")


def test_variable_mapping_validation_text_field_accepted():
    """Test that TEXT_FIELD is accepted as valid fieldType"""
    print("🧪 Testing TEXT_FIELD is accepted as fieldType...")
    
    service = RuleBasedChatbotService()
    
    # Create a node with valid fieldType
    node_data = Mock()
    node_data.id = "n1"
    node_data.name = "Node with valid fieldType"
    node_data.type = "question"
    node_data.data = Mock()
    node_data.data.text = "Hello {{1}}"
    node_data.data.options = []
    
    # Variable mapping with valid fieldType
    node_data.variableMapping = [
        {
            "componentType": "BODY",
            "variable": "1",
            "entity": "contact",
            "internalName": "firstName",
            "fallbackValue": "",
            "fieldType": "TEXT_FIELD"  # Valid fieldType
        }
    ]
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [node_data]
    flow_data.edges = []
    
    # This should not raise a validation error
    try:
        service.validate_flow(flow_data)
        print("✅ TEXT_FIELD fieldType passed validation")
        assert True
    except RuleBasedFlowValidationError as e:
        # Check that the error is not about fieldType
        assert "fieldtype" not in str(e).lower()
        print(f"❌ Unexpected validation error: {e}")


def test_validate_flow_list_node_success():
    """Test successful validation of list node with required sections"""
    print("🧪 Testing validate_flow with valid list node...")
    
    service = RuleBasedChatbotService()
    
    # Create valid list node
    list_node = ChatbotNodeCreate(
        id="list-1",
        name="list_node",
        type="list",
        position=NodePosition(x=100, y=100),
        data=NodeData(
            header=NodeHeader(format="text", text="Choose an option"),
            body="Please select from the list below",
            footer="Thank you for your selection",
            menuButton="View All",
            sections=[
                NodeSection(
                    title="Main Options",
                    rows=[
                        {"id": "option1", "text": "First Option"},
                        {"id": "option2", "text": "Second Option"}
                    ]
                )
            ]
        )
    )
    
    flow = RuleBasedChatbotFlow(
        nodes=[list_node],
        edges=[]
    )
    
    # Should not raise any exception
    result = service.validate_flow(flow)
    assert result["valid"] is True
    assert result["summary"]["total_nodes"] == 1
    assert result["summary"]["node_types"]["list"] == 1
    print("✅ List node validation passed")


def test_validate_flow_list_node_missing_sections():
    """Test validation failure when list node has no sections"""
    print("🧪 Testing validate_flow with list node missing sections...")
    
    service = RuleBasedChatbotService()
    
    # Create invalid list node without sections
    list_node = ChatbotNodeCreate(
        id="list-1",
        name="list_node",
        type="list",
        position=NodePosition(x=100, y=100),
        data=NodeData(
            header=NodeHeader(format="text", text="Choose an option"),
            body="Please select from the list below",
            footer="Thank you for your selection",
            menuButton="View All",
            sections=[]  # Empty sections - should fail validation
        )
    )
    
    flow = RuleBasedChatbotFlow(
        nodes=[list_node],
        edges=[]
    )
    
    # Should raise validation error
    with pytest.raises(RuleBasedFlowValidationError) as exc_info:
        service.validate_flow(flow)
    
    # Check that the error message contains the validation error
    error_message = str(exc_info.value)
    assert "List node must have sections" in error_message or "validation failed" in error_message
    print("✅ List node missing sections validation passed")


def test_validate_flow_list_node_with_entity_fields():
    """Test validation of list node with entity fields"""
    print("🧪 Testing validate_flow with list node containing entity fields...")
    
    service = RuleBasedChatbotService()
    
    # Create list node with entity fields
    list_node = ChatbotNodeCreate(
        id="list-1",
        name="list_node",
        type="list",
        position=NodePosition(x=100, y=100),
        data=NodeData(
            header=NodeHeader(format="text", text="Question to be ask {{1}}"),
            body="This is list variable {{2}}",
            footer="this is footer",
            menuButton="Click to view all",
            sections=[
                NodeSection(
                    title="adasda",
                    rows=[
                        {"id": "abc", "text": "row title"}
                    ]
                )
            ],
            entityFields=[
                {
                    "entityType": "LEAD",
                    "fieldId": 123,
                    "standard": True,
                    "name": "firstName",
                    "displayName": "First Name"
                }
            ]
        )
    )
    
    flow = RuleBasedChatbotFlow(
        nodes=[list_node],
        edges=[]
    )
    
    # Should not raise any exception
    result = service.validate_flow(flow)
    assert result["valid"] is True
    assert result["summary"]["total_nodes"] == 1
    assert result["summary"]["node_types"]["list"] == 1
    print("✅ List node with entity fields validation passed")


def test_validate_flow_list_node_invalid_type():
    """Test validation failure when node type is not 'list' but has list data"""
    print("🧪 Testing validate_flow with invalid node type...")
    
    service = RuleBasedChatbotService()
    
    # Create node with invalid type but list data
    invalid_node = ChatbotNodeCreate(
        id="invalid-1",
        name="invalid_node",
        type="invalid_type",  # Invalid type
        position=NodePosition(x=100, y=100),
        data=NodeData(
            sections=[
                NodeSection(
                    title="Test",
                    rows=[{"id": "test", "text": "Test"}]
                )
            ]
        )
    )
    
    flow = RuleBasedChatbotFlow(
        nodes=[invalid_node],
        edges=[]
    )
    
    # Should raise validation error
    with pytest.raises(RuleBasedFlowValidationError) as exc_info:
        service.validate_flow(flow)
    
    assert any("Invalid node type" in error for error in exc_info.value.details["validationErrors"])
    print("✅ Invalid node type validation passed")


def test_variable_mapping_validation_text_rejected():
    """Test that TEXT is rejected as invalid fieldType"""
    print("🧪 Testing TEXT is rejected as fieldType...")
    
    service = RuleBasedChatbotService()
    
    # Create a node with invalid fieldType
    node_data = Mock()
    node_data.id = "n1"
    node_data.name = "Node with invalid fieldType"
    node_data.type = "question"
    node_data.data = Mock()
    node_data.data.text = "Hello {{1}}"
    node_data.data.options = []
    
    # Variable mapping with invalid fieldType (old TEXT instead of TEXT_FIELD)
    node_data.variableMapping = [
        {
            "componentType": "BODY",
            "variable": "1",
            "entity": "contact",
            "internalName": "firstName",
            "fallbackValue": "",
            "fieldType": "TEXT"  # Invalid - should be TEXT_FIELD
        }
    ]
    
    # Create flow data
    flow_data = Mock()
    flow_data.nodes = [node_data]
    flow_data.edges = []
    
    # This should raise a validation error about invalid fieldType
    try:
        service.validate_flow(flow_data)
        print("❌ Should have failed validation for TEXT fieldType")
        assert False
    except RuleBasedFlowValidationError as e:
        # Check that the validation errors contain fieldType-related error
        validation_errors = e.details.get("validationErrors", [])
        fieldtype_error_found = any("fieldtype" in error.lower() for error in validation_errors)
        text_error_found = any("text" in error.lower() for error in validation_errors)
        assert fieldtype_error_found, f"Expected fieldType-related error in validation errors: {validation_errors}"
        assert text_error_found, f"Expected text-related error in validation errors: {validation_errors}"
        print("✅ TEXT fieldType correctly rejected")


# Test cases for loop detection error code
def test_loop_detection_error_code():
    """Test that loop detection errors use the correct error code 041125"""
    print("🧪 Testing loop detection error code...")
    
    # Create a RuleBasedFlowLoopDetectionError directly
    error = RuleBasedFlowLoopDetectionError(
        validation_errors=["Simple cycle detected: A → B → A"],
        validation_warnings=["Break cycles by adding question nodes"],
        message="Infinite loops detected in chatbot flow"
    )
    
    # Verify the error code
    assert error.error_code == "041125"
    assert "Infinite loops detected" in error.message
    assert "validationErrors" in error.details
    assert "validationWarnings" in error.details
    assert len(error.details["validationErrors"]) == 1
    assert len(error.details["validationWarnings"]) == 1
    
    print("✅ Loop detection error code is correct (041125)")


def test_loop_detection_error_resource_conversion():
    """Test that loop detection error converts to ErrorResource correctly"""
    print("🧪 Testing loop detection error resource conversion...")
    
    error = RuleBasedFlowLoopDetectionError(
        validation_errors=[
            "Simple cycle detected: A → B → A",
            "Strongly connected component detected: C → D → E → C"
        ],
        validation_warnings=[
            "Break cycles by adding question nodes",
            "Consider adding 'end conversation' nodes"
        ],
        message="Infinite loops detected in chatbot flow"
    )
    
    # Convert to ErrorResource
    error_resource = error.to_error_resource()
    
    # Verify the structure
    assert error_resource.errorCode == "041125"
    assert error_resource.message == "Infinite loops detected in chatbot flow"
    assert len(error_resource.fieldErrors) == 4  # 2 errors + 2 warnings
    
    # Check field error structure
    field_names = [fe.field for fe in error_resource.fieldErrors]
    field_messages = [fe.message for fe in error_resource.fieldErrors]
    
    # Should have loop detection errors
    assert any("loop.detection_1" in field for field in field_names)
    assert any("loop.detection_2" in field for field in field_names)
    
    # Should have loop recommendations
    assert any("loop.recommendation_1" in field for field in field_names)
    assert any("loop.recommendation_2" in field for field in field_names)
    
    # Check that error messages are preserved
    assert any("Simple cycle detected" in msg for msg in field_messages)
    assert any("Strongly connected component detected" in msg for msg in field_messages)
    assert any("Break cycles" in msg for msg in field_messages)
    assert any("end conversation" in msg for msg in field_messages)
    
    print("✅ Loop detection error resource conversion works correctly")


def test_loop_detection_vs_validation_error_codes():
    """Test that loop detection and validation errors have different error codes"""
    print("🧪 Testing error code separation...")
    
    # Create validation error
    validation_error = RuleBasedFlowValidationError(
        validation_errors=["Flow must contain at least one node"],
        message="Rule-based chatbot flow validation failed"
    )
    
    # Create loop detection error
    loop_error = RuleBasedFlowLoopDetectionError(
        validation_errors=["Simple cycle detected: A → B → A"],
        message="Infinite loops detected in chatbot flow"
    )
    
    # Verify different error codes
    assert validation_error.error_code == "041120"
    assert loop_error.error_code == "041125"
    assert validation_error.error_code != loop_error.error_code
    
    # Verify different error resource codes
    validation_resource = validation_error.to_error_resource()
    loop_resource = loop_error.to_error_resource()
    
    assert validation_resource.errorCode == "041120"
    assert loop_resource.errorCode == "041125"
    assert validation_resource.errorCode != loop_resource.errorCode
    
    print("✅ Error codes are properly separated")
    print(f"✅ Validation error code: {validation_error.error_code}")
    print(f"✅ Loop detection error code: {loop_error.error_code}")


@patch('app.services.loop_detection_service.LoopDetectionService')
def test_create_flow_with_loop_detection_error(mock_loop_service):
    """Test that create_flow raises RuleBasedFlowLoopDetectionError when loops are detected"""
    print("🧪 Testing create_flow with loop detection error...")
    
    service = RuleBasedChatbotService()
    mock_db = Mock()
    
    # Mock the loop detection service to detect loops
    mock_loop_instance = Mock()
    mock_loop_instance._detect_simple_cycles.return_value = [
        {
            "type": "simple_cycle",
            "cycle": ["A", "B", "A"],
            "length": 2,
            "nodes": ["A", "B"]
        }
    ]
    mock_loop_instance._detect_strongly_connected_components.return_value = []
    mock_loop_instance._detect_long_chains.return_value = []
    mock_loop_instance._generate_warnings.return_value = ["Simple cycle detected: A → B → A"]
    mock_loop_instance._generate_recommendations.return_value = ["Break cycles by adding question nodes"]
    mock_loop_service.return_value = mock_loop_instance
    
    # Create flow with potential loop
    flow_with_loop = RuleBasedChatbotFlow(
        nodes=[
            ChatbotNodeCreate(
                id="node-A",
                name="A",
                type="sendMessage",
                position=NodePosition(x=100, y=100),
                data=NodeData(text="Message A"),
                variableMapping=[]
            ),
            ChatbotNodeCreate(
                id="node-B",
                name="B",
                type="sendMessage",
                position=NodePosition(x=200, y=100),
                data=NodeData(text="Message B"),
                variableMapping=[]
            )
        ],
        edges=[
            ChatbotEdgeCreate(
                id="edge-1",
                source="A",
                target="B",
                sourceHandle="default",
                targetHandle="start"
            ),
            ChatbotEdgeCreate(
                id="edge-2",
                source="B",
                target="A",  # This creates a loop
                sourceHandle="default",
                targetHandle="start"
            )
        ]
    )
    
    try:
        service.create_flow(mock_db, "chatbot-123", 12345, flow_with_loop)
        print("❌ create_flow should have failed with loop detection error")
        assert False, "Should have failed with loop detection error"
    except RuleBasedFlowLoopDetectionError as e:
        print(f"✅ create_flow correctly failed with loop detection error: {e}")
        
        # Verify the error code
        assert e.error_code == "041125"
        assert "Infinite loops detected" in e.message
        
        # Verify rollback was called
        mock_db.rollback.assert_called_once()
        
        # Verify error details
        assert "chatbot_id" in e.details
        assert e.details["chatbot_id"] == "chatbot-123"
        assert "operation" in e.details
        assert e.details["operation"] == "loop_detection"
        
        print("✅ Loop detection error handling works correctly")
    except Exception as e:
        print(f"❌ Unexpected error type: {type(e)} - {e}")
        assert False, f"Expected RuleBasedFlowLoopDetectionError, got {type(e)}"


@patch('app.services.loop_detection_service.LoopDetectionService')
def test_create_flow_validation_error_still_uses_validation_code(mock_loop_service):
    """Test that validation errors still use the validation error code, not loop detection code"""
    print("🧪 Testing that validation errors still use validation code...")
    
    service = RuleBasedChatbotService()
    mock_db = Mock()
    
    # Mock the loop detection service to not detect loops
    mock_loop_instance = Mock()
    mock_loop_instance._detect_simple_cycles.return_value = []
    mock_loop_instance._detect_strongly_connected_components.return_value = []
    mock_loop_instance._detect_long_chains.return_value = []
    mock_loop_service.return_value = mock_loop_instance
    
    # Create flow with validation error (empty flow)
    invalid_flow = RuleBasedChatbotFlow(
        nodes=[],  # Empty nodes - should cause validation error
        edges=[]
    )
    
    try:
        service.create_flow(mock_db, "chatbot-123", 12345, invalid_flow)
        print("❌ create_flow should have failed with validation error")
        assert False, "Should have failed with validation error"
    except RuleBasedFlowValidationError as e:
        print(f"✅ create_flow correctly failed with validation error: {e}")
        
        # Verify the error code is still validation code, not loop detection code
        assert e.error_code == "041120"
        assert e.error_code != "041125"  # Should not be loop detection code
        
        # Verify rollback was called
        mock_db.rollback.assert_called_once()
        
        print("✅ Validation errors still use validation error code (041120)")
    except RuleBasedFlowLoopDetectionError as e:
        print(f"❌ Should not have raised loop detection error: {e}")
        assert False, "Should have raised validation error, not loop detection error"
    except Exception as e:
        print(f"❌ Unexpected error type: {type(e)} - {e}")
        assert False, f"Expected RuleBasedFlowValidationError, got {type(e)}"


def test_loop_detection_error_with_detailed_loop_information():
    """Test loop detection error with detailed loop information"""
    print("🧪 Testing loop detection error with detailed information...")
    
    error = RuleBasedFlowLoopDetectionError(
            validation_errors=[
                "Simple cycle detected: A → B → C → A (length: 3)",
                "Strongly connected component detected: D → E → F → D (length: 3)"
            ],
            validation_warnings=[
                "Break cycles by adding question nodes or condition nodes",
                "Consider adding 'end conversation' nodes to terminate flows"
            ],
            message="Infinite loops detected in chatbot flow",
            details={
                "chatbot_id": "chatbot-456",
                "operation": "loop_detection",
                "simple_cycles": [
                    {
                        "type": "simple_cycle",
                        "cycle": ["A", "B", "C", "A"],
                        "length": 3,
                        "nodes": ["A", "B", "C"]
                    }
                ],
                "scc_loops": [
                    {
                        "type": "strongly_connected_component",
                        "scc": ["D", "E", "F", "D"],
                        "length": 3,
                        "nodes": ["D", "E", "F"]
                    }
                ],
                "chain_issues": [],
                "warnings": ["Simple cycle detected: A → B → C → A (length: 3)"],
                "recommendations": ["Break cycles by adding question nodes"],
                "validationErrors": [
                    "Simple cycle detected: A → B → C → A (length: 3)",
                    "Strongly connected component detected: D → E → F → D (length: 3)"
                ],
                "validationWarnings": [
                    "Break cycles by adding question nodes or condition nodes",
                    "Consider adding 'end conversation' nodes to terminate flows"
                ]
            }
        )
    
    # Verify basic properties
    assert error.error_code == "041125"
    assert "Infinite loops detected" in error.message
    
    # Verify detailed information is preserved
    details = error.details
    assert details["chatbot_id"] == "chatbot-456"
    assert details["operation"] == "loop_detection"
    assert len(details["simple_cycles"]) == 1
    assert len(details["scc_loops"]) == 1
    assert len(details["chain_issues"]) == 0
    
    # Verify error resource conversion includes all details
    error_resource = error.to_error_resource()
    field_messages = [fe.message for fe in error_resource.fieldErrors]
    
    # Should contain specific cycle information
    # The field messages come from validation_errors and validation_warnings
    assert any("A → B → C → A" in msg for msg in field_messages)
    assert any("D → E → F → D" in msg for msg in field_messages)
    assert any("Break cycles" in msg for msg in field_messages)
    assert any("end conversation" in msg for msg in field_messages)
    
    print("✅ Loop detection error with detailed information works correctly")


def test_loop_detection_long_chain_warning_only():
    """Test that long chains are warnings only, not blocking errors"""
    print("🧪 Testing that long chains are warnings only, not blocking errors...")
    
    # Long chains should NOT cause RuleBasedFlowLoopDetectionError
    # They should be logged as warnings but allow flow creation to continue
    
    # Test that we can create a RuleBasedFlowLoopDetectionError with actual cycles
    # (This is just to test the error class still works for actual cycles)
    error = RuleBasedFlowLoopDetectionError(
        validation_errors=[
            "Simple cycle detected: A → B → A"
        ],
        validation_warnings=[
            "Break cycles by adding question nodes"
        ],
        message="Infinite loops detected in chatbot flow",
        details={
            "chatbot_id": "chatbot-789",
            "operation": "loop_detection",
            "simple_cycles": [
                {
                    "type": "simple_cycle",
                    "cycle": ["A", "B", "A"],
                    "length": 2,
                    "nodes": ["A", "B"]
                }
            ],
            "scc_loops": [],
            "chain_issues": [
                {
                    "type": "long_chain",
                    "chain": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"],
                    "length": 11,
                    "start_node": "A",
                    "nodes": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"]
                }
            ],
            "warnings": ["Simple cycle detected: A → B → A"],
            "recommendations": ["Break cycles by adding question nodes"],
            "validationErrors": ["Simple cycle detected: A → B → A"],
            "validationWarnings": ["Break cycles by adding question nodes"]
        }
    )
    
    # Verify basic properties
    assert error.error_code == "041125"
    assert "Infinite loops detected" in error.message
    
    # Verify error resource conversion
    error_resource = error.to_error_resource()
    field_messages = [fe.message for fe in error_resource.fieldErrors]
    field_names = [fe.field for fe in error_resource.fieldErrors]
    
    # Should contain the cycle detection message, not long chain
    assert any("Simple cycle detected: A → B → A" in msg for msg in field_messages)
    assert any("Break cycles" in msg for msg in field_messages)
    
    # Should have proper field names
    assert any("loop.detection_1" in field for field in field_names)
    assert any("loop.recommendation_1" in field for field in field_names)
    
    # Verify the exact error message format
    assert error_resource.errorCode == "041125"
    assert error_resource.message == "Infinite loops detected in chatbot flow"
    assert len(error_resource.fieldErrors) == 2  # 1 error + 1 recommendation
    
    print("✅ Long chains are warnings only, not blocking errors")


def test_loop_detection_error_message_format():
    """Test that the error message format works for actual cycles (not long chains)"""
    print("🧪 Testing error message format for actual cycles...")
    
    # Create error for actual cycles (not long chains, since long chains are now warnings only)
    error = RuleBasedFlowLoopDetectionError(
        validation_errors=[
            "Simple cycle detected: A → B → C → A"
        ],
        validation_warnings=[
            "Break cycles by adding question nodes or condition nodes",
            "Consider adding 'end conversation' nodes to terminate flows"
        ],
        message="Infinite loops detected in chatbot flow",
        details={
            "validationErrors": [
                "Simple cycle detected: A → B → C → A"
            ],
            "validationWarnings": [
                "Break cycles by adding question nodes or condition nodes",
                "Consider adding 'end conversation' nodes to terminate flows"
            ]
        }
    )
    
    # Verify the error has the right structure for generating this message
    assert error.error_code == "041125"
    assert "Infinite loops detected in chatbot flow" in error.message
    assert len(error.details["validationErrors"]) == 1
    assert len(error.details["validationWarnings"]) == 2
    
    # Verify error resource conversion produces the right field errors
    error_resource = error.to_error_resource()
    field_messages = [fe.message for fe in error_resource.fieldErrors]
    
    # Check that all expected messages are present
    assert "Simple cycle detected: A → B → C → A" in field_messages
    assert "Break cycles by adding question nodes or condition nodes" in field_messages
    assert "Consider adding 'end conversation' nodes to terminate flows" in field_messages
    
    print("✅ Error message format works for actual cycles")


@patch('app.services.loop_detection_service.LoopDetectionService')
def test_create_flow_with_long_chains_allows_creation(mock_loop_service):
    """Test that long chains are logged as warnings but don't block flow creation"""
    print("🧪 Testing that long chains allow flow creation...")
    
    service = RuleBasedChatbotService()
    mock_db = Mock()
    
    # Mock the loop detection service to detect long chains but no actual cycles
    mock_loop_instance = Mock()
    mock_loop_instance._detect_simple_cycles.return_value = []  # No cycles
    mock_loop_instance._detect_strongly_connected_components.return_value = []  # No cycles
    mock_loop_instance._detect_long_chains.return_value = [  # Long chains detected
        {
            "type": "long_chain",
            "chain": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"],
            "length": 11,
            "start_node": "A",
            "nodes": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"]
        }
    ]
    mock_loop_instance._generate_warnings.return_value = ["Long chain detected: (length: 11, max allowed: 10)"]
    mock_loop_instance._generate_recommendations.return_value = [
        "Reduce chain lengths by breaking long sequences into smaller chunks",
        "Add intermediate question nodes to break up long sendMessage chains"
    ]
    mock_loop_service.return_value = mock_loop_instance
    
    # Create flow with long chain (no cycles)
    flow_with_long_chain = RuleBasedChatbotFlow(
        nodes=[
            ChatbotNodeCreate(
                id="node-A",
                name="A",
                type="sendMessage",
                position=NodePosition(x=100, y=100),
                data=NodeData(text="Message A"),
                variableMapping=[]
            ),
            ChatbotNodeCreate(
                id="node-B",
                name="B",
                type="sendMessage",
                position=NodePosition(x=200, y=100),
                data=NodeData(text="Message B"),
                variableMapping=[]
            )
        ],
        edges=[
            ChatbotEdgeCreate(
                id="edge-1",
                source="A",
                target="B",
                sourceHandle="default",
                targetHandle="start"
            )
        ]
    )
    
    # Mock the other methods that would be called during flow creation
    with patch.object(service, '_delete_existing_flow'), \
         patch.object(service, '_create_nodes', return_value=[]), \
         patch.object(service, '_create_edges', return_value=[]), \
         patch.object(service, '_calculate_and_store_first_nodes'), \
         patch.object(service, '_validate_first_node_consistency'):
        
        try:
            result = service.create_flow(mock_db, "chatbot-123", 12345, flow_with_long_chain)
            
            # Should succeed even with long chains
            assert result is not None
            assert "message" in result
            assert "successfully" in result["message"]
            
            # Verify no rollback was called (since it succeeded)
            mock_db.rollback.assert_not_called()
            
            # Verify commit was called
            mock_db.commit.assert_called_once()
            
            print("✅ Flow creation succeeded with long chains (as expected)")
            
        except Exception as e:
            print(f"❌ Flow creation failed when it should have succeeded: {e}")
            assert False, f"Flow creation should succeed with long chains, but failed: {e}"

    def test_button_click_handling(self):
        """Test that button clicks are properly handled in conversation flow"""
        print("🧪 Testing button click handling...")
        
        service = RuleBasedChatbotService()
        
        # Create a flow with a buttons node and outgoing edge
        # Frontend sends 'id' field when button is clicked, so edges should use button IDs
        flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-buttons-click",
                    name="button_click_test",
                    type="buttons",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header={
                            "format": "text",
                            "text": "Choose an option:",
                            "mediaFile": None
                        },
                        body="Please select one of the options below:",
                        footer="Thank you for your time",
                        buttons=[
                            NodeButton(name="btn-option-1", text="Option 1", position=0),
                            NodeButton(name="btn-option-2", text="Option 2", position=1)
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-next",
                    name="next_node",
                    type="sendMessage",
                    position=NodePosition(x=200, y=100),
                    data=NodeData(text="You selected an option!"),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="button_click_test",
                    target="next_node",
                    sourceHandle="btn-option-1"  # This should match the button's 'name' field (which becomes 'id')
                ),
                ChatbotEdgeCreate(
                    id="edge-2", 
                    source="button_click_test",
                    target="next_node",
                    sourceHandle="btn-option-2"  # This should match the button's 'name' field (which becomes 'id')
                )
            ]
        )
        
        # Should validate successfully
        validation_result = service.validate_flow(flow_data)
        assert validation_result["valid"] == True
        # Warnings about orphaned nodes are expected for buttons nodes
        print("✅ Button click handling validation passed")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
